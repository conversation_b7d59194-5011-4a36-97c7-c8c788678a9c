# -*- coding: utf-8 -*-
"""
اختبار معلومات الموافقة في التقرير
"""

def test_leave_approvals():
    """اختبار معلومات الموافقة للإجازات"""
    print("=== اختبار معلومات الموافقة ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن إجازات موافق عليها
    leaves = env['hr.leave'].search([
        ('state', '=', 'validate')
    ], limit=5)
    
    print("عدد الإجازات الموافق عليها: %s" % len(leaves))
    
    if not leaves:
        print("لا توجد إجازات موافق عليها للاختبار")
        return
    
    print("-" * 80)
    
    for leave in leaves:
        print("الإجازة: %s - %s" % (leave.holiday_status_id.name, leave.employee_id.name))
        print("  الفترة: %s إلى %s" % (leave.date_from.date(), leave.date_to.date()))
        print("  عدد الأيام: %s" % leave.number_of_days)
        print("  نوع التحقق: %s" % leave.validation_type)
        print("  الحالة: %s" % leave.state)
        
        # معلومات الموافقة
        if leave.first_approver_id:
            print("  الموافق الأول: %s" % leave.first_approver_id.name)
        else:
            print("  الموافق الأول: غير محدد")
            
        if leave.second_approver_id:
            print("  الموافق الثاني: %s" % leave.second_approver_id.name)
        else:
            print("  الموافق الثاني: غير محدد")
        
        print("-" * 80)

def test_approval_info_function():
    """اختبار دالة معلومات الموافقة في wizard"""
    print("=== اختبار دالة معلومات الموافقة ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف لديه إجازات موافق عليها
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return
    
    employee = leave.employee_id
    print("اختبار معلومات الموافقة للموظف: %s" % employee.name)
    
    # إنشاء wizard للاختبار
    wizard_vals = {
        'date_from': '2024-01-01',
        'date_to': '2024-12-31',
        'employee_id': employee.id,
        'all_employees': False,
        'state_filter': 'validate'
    }
    
    wizard = env['hr.leave.report.wizard'].create(wizard_vals)
    print("تم إنشاء wizard للاختبار")
    
    # اختبار دالة معلومات الموافقة
    approval_info = wizard.get_employee_approval_info(employee.id)
    
    print("معلومات الموافقة:")
    print("  عرض الموافقة: %s" % approval_info.get('show_approval', False))
    print("  عدد الموافقات: %s" % approval_info.get('total_approvals', 0))
    
    if approval_info.get('approvals'):
        print("  تفاصيل الموافقات:")
        for i, approval in enumerate(approval_info['approvals'], 1):
            print("    %s. نوع الإجازة: %s" % (i, approval['leave_type']))
            print("       الفترة: %s إلى %s" % (approval['date_from'], approval['date_to']))
            print("       عدد الأيام: %s" % approval['number_of_days'])
            print("       نوع التحقق: %s" % approval['validation_type'])
            print("       الموافق: %s" % approval['approved_by'])
            print("       المعتمد: %s" % approval['validated_by'])
            print()
    
    # حذف wizard الاختبار
    wizard.unlink()

def test_report_with_approval():
    """اختبار إنشاء تقرير مع معلومات الموافقة"""
    print("=== اختبار التقرير مع معلومات الموافقة ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف لديه إجازات موافق عليها
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return
    
    employee = leave.employee_id
    print("إنشاء تقرير مع معلومات الموافقة للموظف: %s" % employee.name)
    
    # إنشاء wizard التقرير
    wizard_vals = {
        'date_from': '2024-01-01',
        'date_to': '2024-12-31',
        'employee_id': employee.id,
        'all_employees': False,
        'state_filter': 'validate'
    }
    
    wizard = env['hr.leave.report.wizard'].create(wizard_vals)
    
    # الحصول على بيانات التقرير
    leave_data = wizard.get_leave_data()
    print("عدد الإجازات في التقرير: %s" % len(leave_data))
    
    # فحص معلومات الموافقة في البيانات
    if leave_data:
        print("معلومات الموافقة في البيانات:")
        for i, leave_item in enumerate(leave_data[:3], 1):  # أول 3 إجازات
            print("  %s. %s:" % (i, leave_item['leave_type']))
            print("     الموافق الأول: %s" % leave_item.get('first_approver', 'غير محدد'))
            print("     الموافق الثاني: %s" % leave_item.get('second_approver', 'غير محدد'))
            print("     نوع التحقق: %s" % leave_item.get('validation_type', 'غير محدد'))
    
    # اختبار إنشاء التقرير
    try:
        result = wizard.generate_report()
        print("✓ تم إنشاء التقرير مع معلومات الموافقة بنجاح")
        print("نوع النتيجة: %s" % result.get('type', 'غير محدد'))
    except Exception as e:
        print("✗ خطأ في إنشاء التقرير: %s" % str(e))
    
    # حذف wizard
    wizard.unlink()

def create_test_leave_with_approval():
    """إنشاء إجازة اختبارية مع موافقة"""
    print("=== إنشاء إجازة اختبارية مع موافقة ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف
    employee = env['hr.employee'].search([], limit=1)
    if not employee:
        print("لا يوجد موظفين في النظام")
        return
    
    # البحث عن نوع إجازة
    leave_type = env['hr.leave.type'].search([], limit=1)
    if not leave_type:
        print("لا يوجد أنواع إجازات في النظام")
        return
    
    # البحث عن مستخدم للموافقة
    approver = env['hr.employee'].search([('id', '!=', employee.id)], limit=1)
    if not approver:
        print("لا يوجد موظف آخر للموافقة")
        return
    
    print("إنشاء إجازة اختبارية:")
    print("  الموظف: %s" % employee.name)
    print("  نوع الإجازة: %s" % leave_type.name)
    print("  الموافق: %s" % approver.name)
    
    # إنشاء إجازة
    leave = env['hr.leave'].create({
        'name': 'Test Leave with Approval',
        'employee_id': employee.id,
        'holiday_status_id': leave_type.id,
        'request_date_from': '2024-06-01',
        'request_date_to': '2024-06-05',
        'number_of_days': 5,
        'state': 'validate',
        'first_approver_id': approver.id
    })
    
    print("✓ تم إنشاء الإجازة بنجاح، ID: %s" % leave.id)
    print("الحالة: %s" % leave.state)
    print("الموافق الأول: %s" % (leave.first_approver_id.name if leave.first_approver_id else 'غير محدد'))

# تشغيل الاختبارات
if __name__ == "__main__":
    print("استخدم الدوال التالية:")
    print("1. test_leave_approvals() - لاختبار معلومات الموافقة للإجازات")
    print("2. test_approval_info_function() - لاختبار دالة معلومات الموافقة")
    print("3. test_report_with_approval() - لاختبار التقرير مع معلومات الموافقة")
    print("4. create_test_leave_with_approval() - لإنشاء إجازة اختبارية مع موافقة")
    print("\nمثال:")
    print("exec(open('/path/to/hr_leave_reports/test_approval_info.py').read())")
    print("test_leave_approvals()")
