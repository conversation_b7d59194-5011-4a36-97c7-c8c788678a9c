<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Job Description Form View -->
        <record id="view_hr_job_description_form" model="ir.ui.view">
            <field name="name">hr.job.description.form</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <form string="Job Description">
                    <header>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="job_title" placeholder="e.g. Senior Software Developer"/>
                            </h1>
                            <h2>
                                <field name="employee_id" placeholder="Select Employee"/>
                            </h2>
                        </div>

                        <!-- Basic Information Section -->
                        <group>
                            <group string="Position Details">
                                <field name="department_id" options="{'no_create': True}"/>
                                <field name="job_level"/>
                                <field name="work_location" placeholder="e.g. Main Office, Remote, etc."/>
                            </group>
                            <group string="Reporting &amp; Dates">
                                <field name="reports_to" options="{'no_create': True}"/>
                                <field name="effective_date"/>
                                <field name="last_update_date" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>

                        <!-- Job Description Content -->
                        <notebook>
                            <page name="responsibilities" string="📋 Responsibilities">
                                <separator string="Main Responsibilities &amp; Duties"/>
                                <field name="main_responsibilities" nolabel="1"
                                       placeholder="• Describe the primary responsibilities and daily duties&#10;• List key tasks and deliverables&#10;• Include any supervisory responsibilities&#10;• Mention collaboration requirements"/>
                            </page>

                            <page name="qualifications" string="🎓 Qualifications">
                                <separator string="Required Qualifications &amp; Education"/>
                                <field name="required_qualifications" nolabel="1"
                                       placeholder="• Educational requirements (degree, certifications)&#10;• Professional certifications needed&#10;• Minimum experience requirements&#10;• Industry-specific qualifications"/>
                            </page>

                            <page name="skills" string="💼 Skills &amp; Experience">

                                <!-- Sub-tabs for Skills and Experience -->
                                <notebook>

                                    <!-- Skills Sub-tab -->
                                    <page name="skills_sub_tab" string="🎯 Skills">
                                        <separator string="Required Skills &amp; Competencies"/>
                                        <field name="required_skills" nolabel="1"
                                               placeholder="• Technical skills required&#10;• Software proficiency needed&#10;• Soft skills and competencies&#10;• Language requirements&#10;• Communication skills"/>
                                    </page>

                                    <!-- Experience Sub-tab -->
                                    <page name="experience_sub_tab" string="📈 Experience">
                                        <separator string="Required Experience &amp; Background"/>
                                        <field name="required_experience" nolabel="1"
                                               placeholder="• Years of experience needed&#10;• Industry experience required&#10;• Specific project experience&#10;• Leadership experience&#10;• Management experience"/>
                                    </page>

                                </notebook>

                                <!-- Legacy field for backward compatibility -->
                                <separator string="Combined Skills &amp; Experience (Legacy)" attrs="{'invisible': [('skills_experience', '=', False)]}"/>
                                <field name="skills_experience" nolabel="1" readonly="1"
                                       attrs="{'invisible': [('skills_experience', '=', False)]}"
                                       placeholder="This field contains legacy data. Please use the separate Skills and Experience fields above."/>
                            </page>

                            <page name="additional" string="📝 Additional Info">
                                <separator string="Additional Notes &amp; Requirements"/>
                                <field name="additional_notes" nolabel="1"
                                       placeholder="• Special requirements or conditions&#10;• Travel requirements&#10;• Physical demands&#10;• Working hours or schedule&#10;• Any other relevant information"/>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Job Description Tree View -->
        <record id="view_hr_job_description_tree" model="ir.ui.view">
            <field name="name">hr.job.description.tree</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <tree string="Job Descriptions" decoration-muted="not active">
                    <field name="employee_id"/>
                    <field name="job_title"/>
                    <field name="department_id"/>
                    <field name="job_level"/>
                    <field name="reports_to" optional="hide"/>
                    <field name="work_location" optional="hide"/>
                    <field name="effective_date"/>
                    <field name="last_update_date"/>
                    <field name="active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>

        <!-- Job Description Search View -->
        <record id="view_hr_job_description_search" model="ir.ui.view">
            <field name="name">hr.job.description.search</field>
            <field name="model">hr.job.description</field>
            <field name="arch" type="xml">
                <search string="Job Descriptions">
                    <field name="job_title" string="Job Title"/>
                    <field name="employee_id" string="Employee"/>
                    <field name="department_id" string="Department"/>
                    <field name="job_level" string="Job Level"/>
                    <field name="reports_to" string="Reports To"/>
                    <field name="work_location" string="Work Location"/>

                    <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                    <filter name="inactive" string="Inactive" domain="[('active', '=', False)]"/>

                    <separator/>
                    <filter name="this_month" string="This Month"
                            domain="[('effective_date', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter name="this_year" string="This Year"
                            domain="[('effective_date', '&gt;=', context_today().strftime('%Y-01-01'))]"/>

                    <group expand="0" string="Group By">
                        <filter name="group_department" string="Department"
                                domain="[]" context="{'group_by': 'department_id'}"/>
                        <filter name="group_job_level" string="Job Level"
                                domain="[]" context="{'group_by': 'job_level'}"/>
                        <filter name="group_reports_to" string="Reports To"
                                domain="[]" context="{'group_by': 'reports_to'}"/>
                        <filter name="group_effective_date" string="Effective Date"
                                domain="[]" context="{'group_by': 'effective_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Job Description Action -->
        <record id="action_hr_job_description" model="ir.actions.act_window">
            <field name="name">Job Descriptions</field>
            <field name="res_model">hr.job.description</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_hr_job_description_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new job description
                </p>
                <p>
                    Job descriptions help define roles, responsibilities, and requirements
                    for each position in your organization. Click "Create" to add a new
                    comprehensive job description.
                </p>
            </field>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_hr_job_description"
                  name="Job Descriptions"
                  parent="hr.menu_hr_root"
                  action="action_hr_job_description"
                  sequence="25"
                  groups="hr.group_hr_user"/>

        <!-- Inherit Employee Form View to Add Job Description Tab -->
        <record id="view_employee_form_job_description" model="ir.ui.view">
            <field name="name">hr.employee.form.job.description</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">

                <!-- Add Job Description button to button box -->
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_view_job_descriptions" type="object"
                            class="oe_stat_button" icon="fa-file-text-o"
                            groups="hr.group_hr_user">
                        <field name="job_description_count" widget="statinfo" string="Job Descriptions"/>
                    </button>
                </xpath>

                <!-- Add invisible fields needed for attrs -->
                <xpath expr="//field[@name='active']" position="after">
                    <field name="current_job_description_id" invisible="1"/>
                    <field name="current_responsibilities_display" invisible="1"/>
                    <field name="current_qualifications_display" invisible="1"/>
                    <field name="current_skills_display" invisible="1"/>
                    <field name="current_required_skills_display" invisible="1"/>
                    <field name="current_required_experience_display" invisible="1"/>
                    <field name="current_additional_notes_display" invisible="1"/>
                </xpath>

                <!-- Add the Job Description tab after HR Settings tab -->
                <xpath expr="//page[@name='hr_settings']" position="after">
                    <page name="job_description" string="Job Description" groups="hr.group_hr_user">

                        <!-- Header Section with Actions -->
                        <div class="oe_button_box">
                            <button name="action_view_job_descriptions" type="object"
                                    string="View All Descriptions"
                                    class="oe_link"
                                    attrs="{'invisible': [('job_description_count', '=', 0)]}"/>
                            <button name="create_job_description" type="object"
                                    string="Create New Description"
                                    class="oe_link"/>
                        </div>

                        <!-- No Job Description Message -->
                        <group attrs="{'invisible': [('current_job_description_id', '!=', False)]}"
                               string="Job Description Status">
                            <div class="alert alert-info">
                                <strong>No Job Description Found</strong><br/>
                                <span>No job description has been created for this employee yet.
                                Click "Create New Description" to add a comprehensive job description.</span>
                            </div>
                        </group>

                        <!-- Job Description Details Display -->
                        <div attrs="{'invisible': [('current_job_description_id', '=', False)]}">

                            <!-- Job Description Content Display -->
                            <notebook attrs="{'invisible': [('current_job_description_id', '=', False)]}">

                                <page name="responsibilities_display" string="📋 Responsibilities">
                                    <separator string="Main Responsibilities &amp; Duties"/>
                                    <field name="current_responsibilities_display" readonly="1" nolabel="1"
                                           attrs="{'invisible': [('current_responsibilities_display', '=', False)]}"/>
                                    <div attrs="{'invisible': [('current_responsibilities_display', '!=', False)]}"
                                         class="alert alert-info">
                                        <p><strong>No responsibilities defined yet.</strong></p>
                                        <p>Click "View Full Details" to add detailed job responsibilities.</p>
                                    </div>
                                </page>

                                <page name="qualifications_display" string="🎓 Qualifications">
                                    <separator string="Required Qualifications &amp; Education"/>
                                    <field name="current_qualifications_display" readonly="1" nolabel="1"
                                           attrs="{'invisible': [('current_qualifications_display', '=', False)]}"/>
                                    <div attrs="{'invisible': [('current_qualifications_display', '!=', False)]}"
                                         class="alert alert-info">
                                        <p><strong>No qualifications defined yet.</strong></p>
                                        <p>Click "View Full Details" to add required qualifications.</p>
                                    </div>
                                </page>

                                <page name="skills_display" string="💼 Skills &amp; Experience">

                                    <!-- Sub-tabs for Skills and Experience -->
                                    <notebook attrs="{'invisible': ['&amp;', ('current_required_skills_display', '=', False), ('current_required_experience_display', '=', False)]}">

                                        <!-- Skills Sub-tab -->
                                        <page name="skills_sub_tab" string="🎯 Skills"
                                              attrs="{'invisible': [('current_required_skills_display', '=', False)]}">
                                            <separator string="Required Skills &amp; Competencies"/>
                                            <field name="current_required_skills_display" readonly="1" nolabel="1"/>
                                        </page>

                                        <!-- Experience Sub-tab -->
                                        <page name="experience_sub_tab" string="📈 Experience"
                                              attrs="{'invisible': [('current_required_experience_display', '=', False)]}">
                                            <separator string="Required Experience &amp; Background"/>
                                            <field name="current_required_experience_display" readonly="1" nolabel="1"/>
                                        </page>

                                    </notebook>

                                    <!-- Legacy combined field (if exists and no separate data) -->
                                    <div attrs="{'invisible': ['|', '|', ('current_skills_display', '=', False), ('current_required_skills_display', '!=', False), ('current_required_experience_display', '!=', False)]}">
                                        <separator string="Skills &amp; Experience (Combined)"/>
                                        <field name="current_skills_display" readonly="1" nolabel="1"/>
                                    </div>

                                    <!-- No data message -->
                                    <div attrs="{'invisible': ['|', '|', ('current_required_skills_display', '!=', False), ('current_required_experience_display', '!=', False), ('current_skills_display', '!=', False)]}"
                                         class="alert alert-info">
                                        <p><strong>No skills or experience defined yet.</strong></p>
                                        <p>Click "View Full Details" to add required skills and experience.</p>
                                    </div>
                                </page>

                                <page name="additional_display" string="📝 Additional Info">
                                    <separator string="Additional Notes &amp; Requirements"/>
                                    <field name="current_additional_notes_display" readonly="1" nolabel="1"
                                           attrs="{'invisible': [('current_additional_notes_display', '=', False)]}"/>
                                    <div attrs="{'invisible': [('current_additional_notes_display', '!=', False)]}"
                                         class="alert alert-info">
                                        <p><strong>No additional notes available.</strong></p>
                                        <p>Click "View Full Details" to add additional information.</p>
                                    </div>
                                </page>

                            </notebook>

                        </div>

                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
