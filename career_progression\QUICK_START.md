# دليل البدء السريع - Career Progression

## 🚀 البدء السريع في 5 دقائق

### 1. التثبيت
```bash
# انسخ الموديول إلى مجلد addons
cp -r career_progression /path/to/odoo/addons/

# أعد تشغيل Odoo
sudo systemctl restart odoo

# ثبت الموديول من واجهة Odoo
Apps > Search "Career Progression" > Install
```

### 2. الوصول السريع
بعد التثبيت، ستجد:
- **قائمة جديدة**: Human Resources > Career Progression
- **تبويب جديد**: في نموذج الموظف > Career Path

### 3. إضافة أول حدث وظيفي
1. افتح ملف أي موظف
2. انتقل إلى تبويب "Career Path"
3. اضغط "Add Career Event"
4. املأ البيانات:
   - **Event Title**: مثل "ترقية إلى مدير"
   - **Event Type**: اختر نوع الحدث
   - **Event Date**: تاريخ الحدث
   - **Previous/New Position**: المنصب السابق والجديد
5. احفظ وأكد الحدث

## 📋 أنواع الأحداث المتاحة

| النوع | الوصف | مثال |
|-------|--------|-------|
| **Promotion** | ترقية وظيفية | من مطور إلى مطور أول |
| **Transfer** | نقل بين الإدارات | من IT إلى Sales |
| **Title Change** | تعديل المسمى | من محاسب إلى محاسب أول |
| **Temporary Assignment** | مهمة مؤقتة | قائد فريق مؤقت |
| **Department Change** | تغيير الإدارة | انتقال داخلي |
| **Location Change** | تغيير الموقع | من الفرع الرئيسي للفرع الفرعي |

## 🎯 الميزات الأساسية

### عرض المسار الوظيفي
- **Tree View**: قائمة جميع الأحداث
- **Calendar View**: عرض تقويمي للأحداث
- **Graph View**: تحليل إحصائي
- **Kanban View**: بطاقات منظمة

### البحث والتصفية
```
# أمثلة على البحث
- البحث بالموظف: "أحمد محمد"
- تصفية الترقيات: Filter > Promotions
- أحداث هذا العام: Filter > This Year
- حسب الإدارة: Group By > Department
```

### المرفقات
- أرفق قرارات الترقية
- مستندات النقل
- أي وثائق داعمة

## 🔐 الصلاحيات

| المجموعة | القراءة | الكتابة | الإنشاء | الحذف |
|----------|---------|---------|---------|--------|
| **HR Manager** | ✅ | ✅ | ✅ | ✅ |
| **HR User** | ✅ | ✅ | ✅ | ❌ |
| **Employee** | ✅ | ❌ | ❌ | ❌ |

## 📊 الإحصائيات المتاحة

في تبويب Career Path ستجد:
- **Years in Current Position**: عدد السنوات في المنصب الحالي
- **Total Career Events**: إجمالي الأحداث الوظيفية
- **Last Career Event**: آخر حدث وظيفي

## 🔧 إعدادات متقدمة

### تخصيص أنواع الأحداث
```
Human Resources > Configuration > Career Progression > Event Types
```

### إدارة الصلاحيات
```
Settings > Users & Companies > Groups
```

### عرض التقارير
```
Human Resources > Reporting > Career Progression Analysis
```

## 💡 نصائح للاستخدام الأمثل

### 1. تنظيم البيانات
- استخدم أسماء واضحة للأحداث
- أضف تواريخ دقيقة
- اكتب أسباب مفصلة للتغييرات

### 2. استخدام المرفقات
- أرفق قرارات رسمية
- احفظ نسخ من المراسلات
- وثق التغييرات المهمة

### 3. المتابعة الدورية
- راجع المسارات الوظيفية شهرياً
- حدث البيانات عند حدوث تغييرات
- استخدم التقارير للتحليل

## 🆘 حل المشاكل الشائعة

### المشكلة: التبويب لا يظهر
**الحل**: تأكد من صلاحيات HR User

### المشكلة: لا يمكن إضافة أحداث
**الحل**: تحقق من صلاحيات الكتابة

### المشكلة: خطأ في التواريخ
**الحل**: تأكد من أن تاريخ النهاية بعد تاريخ البداية

### المشكلة: المرفقات لا تظهر
**الحل**: تحقق من إعدادات الملفات في Odoo

## 📞 الدعم الفني

### الوثائق الكاملة
- `README.md` - دليل شامل
- `INSTALLATION.md` - دليل التثبيت
- `CHANGELOG.md` - سجل التغييرات

### الاختبار
```bash
# تشغيل الاختبارات
python -m pytest career_progression/tests/
```

### سجلات النظام
```bash
# عرض سجلات Odoo
tail -f /var/log/odoo/odoo.log | grep career_progression
```

---

## 🎉 مبروك!

أنت الآن جاهز لاستخدام موديول Career Progression بكفاءة. ابدأ بإضافة الأحداث الوظيفية لموظفيك وتتبع مساراتهم المهنية بطريقة احترافية ومنظمة.
