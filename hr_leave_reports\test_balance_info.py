# -*- coding: utf-8 -*-
"""
اختبار معلومات الرصيد في التقرير
"""

def test_employee_balance():
    """اختبار رصيد الإجازات للموظفين"""
    print("=== اختبار رصيد الإجازات ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظفين لديهم إجازات
    employees_with_leaves = env['hr.leave'].search([
        ('state', '=', 'validate')
    ]).mapped('employee_id')
    
    print("عدد الموظفين الذين لديهم إجازات: %s" % len(employees_with_leaves))
    
    if not employees_with_leaves:
        print("لا يوجد موظفين لديهم إجازات موافق عليها")
        return
    
    # اختبار أول 3 موظفين
    for employee in employees_with_leaves[:3]:
        print("-" * 60)
        print("الموظف: %s" % employee.name)
        
        # البحث عن أنواع الإجازات للموظف
        leave_types = env['hr.leave'].search([
            ('employee_id', '=', employee.id),
            ('state', '=', 'validate')
        ]).mapped('holiday_status_id')
        
        print("أنواع الإجازات: %s" % ', '.join(leave_types.mapped('name')))
        
        for leave_type in leave_types:
            print("  نوع الإجازة: %s" % leave_type.name)
            
            # الحصول على بيانات الإجازات
            employee_days = leave_type.with_context(employee_id=employee.id).get_employees_days([employee.id])
            employee_data = employee_days.get(employee.id, {})
            leave_type_data = employee_data.get(leave_type.id, {})
            
            print("    الحد الأقصى: %s أيام" % leave_type_data.get('max_leaves', 0))
            print("    المستخدم: %s أيام" % leave_type_data.get('leaves_taken', 0))
            print("    المتبقي: %s أيام" % leave_type_data.get('remaining_leaves', 0))
            print("    المتبقي الافتراضي: %s أيام" % leave_type_data.get('virtual_remaining_leaves', 0))

def test_balance_calculation():
    """اختبار حساب الرصيد في wizard التقرير"""
    print("=== اختبار حساب الرصيد في التقرير ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن إجازة موافق عليها
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return
    
    print("اختبار الإجازة:")
    print("  الموظف: %s" % leave.employee_id.name)
    print("  نوع الإجازة: %s" % leave.holiday_status_id.name)
    print("  عدد الأيام: %s" % leave.number_of_days)
    print("  من: %s إلى: %s" % (leave.date_from.date(), leave.date_to.date()))
    
    # إنشاء wizard للاختبار
    wizard_vals = {
        'date_from': leave.date_from.date(),
        'date_to': leave.date_to.date(),
        'employee_id': leave.employee_id.id,
        'all_employees': False,
        'state_filter': 'validate'
    }
    
    wizard = env['hr.leave.report.wizard'].create(wizard_vals)
    print("تم إنشاء wizard للاختبار")
    
    # اختبار دالة حساب الرصيد
    balance_info = wizard.get_employee_leave_balance(
        leave.employee_id.id, 
        leave.holiday_status_id.id
    )
    
    print("معلومات الرصيد:")
    print("  نوع الإجازة: %s" % balance_info.get('leave_type_name', ''))
    print("  الحد الأقصى: %s أيام" % balance_info.get('max_leaves', 0))
    print("  المستخدم إجمالي: %s أيام" % balance_info.get('leaves_taken', 0))
    print("  الرصيد قبل الطلب: %s أيام" % balance_info.get('balance_before', 0))
    print("  الأيام المطلوبة: %s أيام" % balance_info.get('requested_days', 0))
    print("  الرصيد بعد الطلب: %s أيام" % balance_info.get('balance_after', 0))
    
    # حذف wizard الاختبار
    wizard.unlink()

def test_report_with_balance():
    """اختبار إنشاء تقرير مع معلومات الرصيد"""
    print("=== اختبار التقرير مع معلومات الرصيد ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف لديه إجازات
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return
    
    print("إنشاء تقرير للموظف: %s" % leave.employee_id.name)
    
    # إنشاء wizard التقرير
    wizard_vals = {
        'date_from': '2024-01-01',
        'date_to': '2024-12-31',
        'employee_id': leave.employee_id.id,
        'all_employees': False,
        'state_filter': 'all'
    }
    
    wizard = env['hr.leave.report.wizard'].create(wizard_vals)
    
    # الحصول على بيانات التقرير
    leave_data = wizard.get_leave_data()
    print("عدد الإجازات في التقرير: %s" % len(leave_data))
    
    # اختبار إنشاء التقرير
    try:
        result = wizard.generate_report()
        print("✓ تم إنشاء التقرير بنجاح")
        print("نوع النتيجة: %s" % result.get('type', 'غير محدد'))
    except Exception as e:
        print("✗ خطأ في إنشاء التقرير: %s" % str(e))
    
    # حذف wizard
    wizard.unlink()

def create_test_allocation():
    """إنشاء تخصيص إجازة اختباري"""
    print("=== إنشاء تخصيص إجازة اختباري ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف
    employee = env['hr.employee'].search([], limit=1)
    if not employee:
        print("لا يوجد موظفين في النظام")
        return
    
    # البحث عن نوع إجازة
    leave_type = env['hr.leave.type'].search([('requires_allocation', '=', 'yes')], limit=1)
    if not leave_type:
        print("لا يوجد أنواع إجازات تتطلب تخصيص")
        return
    
    print("إنشاء تخصيص للموظف: %s" % employee.name)
    print("نوع الإجازة: %s" % leave_type.name)
    
    # إنشاء تخصيص
    allocation = env['hr.leave.allocation'].create({
        'name': 'Test Allocation',
        'employee_id': employee.id,
        'holiday_status_id': leave_type.id,
        'number_of_days': 30,
        'state': 'validate'
    })
    
    print("✓ تم إنشاء التخصيص بنجاح، ID: %s" % allocation.id)
    print("عدد الأيام المخصصة: %s" % allocation.number_of_days)

# تشغيل الاختبارات
if __name__ == "__main__":
    print("استخدم الدوال التالية:")
    print("1. test_employee_balance() - لاختبار رصيد الموظفين")
    print("2. test_balance_calculation() - لاختبار حساب الرصيد")
    print("3. test_report_with_balance() - لاختبار التقرير مع الرصيد")
    print("4. create_test_allocation() - لإنشاء تخصيص اختباري")
    print("\nمثال:")
    print("exec(open('/path/to/hr_leave_reports/test_balance_info.py').read())")
    print("test_employee_balance()")
