# -*- coding: utf-8 -*-

from odoo import fields, models, api


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Job Title Information Fields
    job_title_custom = fields.Char(
        string='Job Title',
        help='Current job title of the employee',
        tracking=True
    )
    
    job_start_date = fields.Date(
        string='Job Start Date',
        help='Date when the employee started in this position',
        tracking=True
    )
    
    job_related_department = fields.Many2one(
        'hr.department',
        string='Related Department',
        help='Department related to this job position',
        tracking=True
    )
    
    job_responsibilities = fields.Text(
        string='Responsibilities',
        help='Detailed description of job responsibilities and duties',
        tracking=True
    )
    
    job_level = fields.Selection([
        ('entry', 'Entry Level'),
        ('junior', 'Junior'),
        ('mid', 'Mid Level'),
        ('senior', 'Senior'),
        ('lead', 'Lead'),
        ('manager', 'Manager'),
        ('director', 'Director'),
        ('executive', 'Executive')
    ], string='Job Level', 
       help='Professional level of the job position',
       tracking=True)
    
    job_additional_notes = fields.Text(
        string='Additional Notes',
        help='Any additional notes or comments about the job position',
        tracking=True
    )

    @api.onchange('job_id')
    def _onchange_job_id(self):
        """Auto-populate job title when job_id changes"""
        if self.job_id:
            self.job_title_custom = self.job_id.name
            if self.job_id.department_id:
                self.job_related_department = self.job_id.department_id.id

    @api.onchange('department_id')
    def _onchange_department_id(self):
        """Auto-populate related department when main department changes"""
        if self.department_id and not self.job_related_department:
            self.job_related_department = self.department_id.id
