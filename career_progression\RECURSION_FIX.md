# إصلاح مشكلة Recursion Error

## 🔍 **المشكلة:**
```
RecursionError: maximum recursion depth exceeded in comparison
```

## ❌ **الأسباب المحتملة:**

### 1. **مشكلة في دالة write()**
```python
def write(self, vals):
    result = super().write(vals)
    self._compute_attachment_count()  # ← هذا يسبب recursion
    return result
```

### 2. **مشكلة في domain lambda**
```python
domain=lambda self: [('res_model', '=', self._name)]  # ← مشكلة محتملة
```

### 3. **مشكلة في _compute_attachment_count**
```python
def _compute_attachment_count(self):
    for record in self:
        if record.id:
            attachments = self.env['ir.attachment'].search([...])  # ← بحث إضافي
```

## ✅ **الحلول المطبقة:**

### 1. **تبسيط دالة write()**
```python
# قبل (مشكلة)
def write(self, vals):
    result = super().write(vals)
    self._compute_attachment_count()  # يسبب recursion
    return result

# بعد (محلول)
def write(self, vals):
    result = super().write(vals)
    return result
```

### 2. **إصلاح domain في attachment_ids**
```python
# قبل (مشكلة محتملة)
attachment_ids = fields.One2many(
    'ir.attachment',
    'res_id',
    domain=lambda self: [('res_model', '=', self._name)],
    context={'default_res_model': 'career.progression'}
)

# بعد (محلول)
attachment_ids = fields.One2many(
    'ir.attachment',
    'res_id',
    domain=[('res_model', '=', 'career.progression')],
)
```

### 3. **تبسيط _compute_attachment_count**
```python
# قبل (معقد)
@api.depends('attachment_ids')
def _compute_attachment_count(self):
    for record in self:
        if record.id:
            attachments = self.env['ir.attachment'].search([
                ('res_model', '=', 'career.progression'),
                ('res_id', '=', record.id)
            ])
            record.attachment_count = len(attachments)
        else:
            record.attachment_count = 0

# بعد (بسيط)
@api.depends('attachment_ids')
def _compute_attachment_count(self):
    for record in self:
        record.attachment_count = len(record.attachment_ids)
```

## 🔧 **التفسير التقني:**

### مشكلة Recursion في Odoo:
1. **دالة write() تستدعي _compute_attachment_count()**
2. **_compute_attachment_count() قد تؤدي إلى تحديث الحقل**
3. **تحديث الحقل يستدعي write() مرة أخرى**
4. **النتيجة: recursion لا نهائي**

### الحل:
- إزالة الاستدعاء اليدوي للـ compute functions
- الاعتماد على آلية Odoo التلقائية
- تبسيط العمليات المعقدة

## 📋 **أفضل الممارسات لتجنب Recursion:**

### 1. **تجنب استدعاء compute functions يدوياً**
```python
# خطأ
def write(self, vals):
    result = super().write(vals)
    self._compute_something()  # لا تفعل هذا
    return result

# صحيح
def write(self, vals):
    result = super().write(vals)
    return result
```

### 2. **استخدام domain ثابت بدلاً من lambda**
```python
# أفضل
domain=[('res_model', '=', 'model.name')]

# تجنب (قد يسبب مشاكل)
domain=lambda self: [('res_model', '=', self._name)]
```

### 3. **تبسيط compute functions**
```python
# بسيط وآمن
@api.depends('related_ids')
def _compute_count(self):
    for record in self:
        record.count = len(record.related_ids)

# معقد وقد يسبب مشاكل
@api.depends('related_ids')
def _compute_count(self):
    for record in self:
        if record.id:
            items = self.env['other.model'].search([...])
            record.count = len(items)
```

### 4. **تجنب التحديثات المتداخلة**
```python
# خطأ
@api.onchange('field1')
def _onchange_field1(self):
    self.field2 = 'value'  # قد يؤدي إلى onchange أخرى

# أفضل
@api.onchange('field1')
def _onchange_field1(self):
    if not self._context.get('skip_onchange'):
        self.field2 = 'value'
```

## 🔍 **تشخيص مشاكل Recursion:**

### 1. **فحص سجلات النظام**
```bash
# البحث عن recursion في اللوج
grep -i "recursion" /var/log/odoo/odoo.log
```

### 2. **استخدام debugger**
```python
import pdb; pdb.set_trace()  # في الدالة المشكوك فيها
```

### 3. **فحص call stack**
```python
import traceback
traceback.print_stack()
```

## ✅ **النتيجة النهائية:**

### الكود المحسن:
```python
class CareerProgression(models.Model):
    _name = 'career.progression'
    
    # حقل المرفقات بسيط وآمن
    attachment_ids = fields.One2many(
        'ir.attachment',
        'res_id',
        domain=[('res_model', '=', 'career.progression')],
        string='Attachments'
    )
    
    # عداد بسيط
    attachment_count = fields.Integer(
        compute='_compute_attachment_count'
    )
    
    # دالة حساب بسيطة
    @api.depends('attachment_ids')
    def _compute_attachment_count(self):
        for record in self:
            record.attachment_count = len(record.attachment_ids)
    
    # دالة write نظيفة
    def write(self, vals):
        return super().write(vals)
```

## 🎉 **تم إصلاح مشكلة Recursion!**

الآن الكود:
- ✅ لا يحتوي على recursion
- ✅ بسيط وواضح
- ✅ يعتمد على آليات Odoo التلقائية
- ✅ آمن ومستقر

## 📞 **نصائح للمستقبل:**

1. **تجنب الاستدعاءات اليدوية للـ compute functions**
2. **استخدم domain ثابت**
3. **اجعل الدوال بسيطة قدر الإمكان**
4. **اختبر التغييرات في بيئة تطوير أولاً**
5. **راقب سجلات النظام للأخطاء**
