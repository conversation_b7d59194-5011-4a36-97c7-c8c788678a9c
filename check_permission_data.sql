-- سكريبت SQL للتحقق من بيانات طلبات الصلاحيات
-- SQL script to check permission request data

-- 1. فحص نوع طلب الصلاحيات
-- Check permission request type
SELECT 
    id,
    name,
    code,
    active,
    show_permission_fields,
    show_password_fields,
    show_usb_fields,
    show_extension_fields,
    show_email_fields
FROM bssic_request_type 
WHERE code = 'permission';

-- 2. فحص عدد طلبات الصلاحيات
-- Check number of permission requests
SELECT 
    COUNT(*) as total_permission_requests
FROM bssic_request br
JOIN bssic_request_type brt ON br.request_type_id = brt.id
WHERE brt.code = 'permission';

-- 3. فحص عينة من طلبات الصلاحيات
-- Check sample permission requests
SELECT 
    br.id,
    br.name,
    br.state,
    br.permission_type,
    br.user_name,
    br.validity_from,
    br.validity_to,
    brt.name as request_type_name,
    brt.show_permission_fields
FROM bssic_request br
JOIN bssic_request_type brt ON br.request_type_id = brt.id
WHERE brt.code = 'permission'
ORDER BY br.id DESC
LIMIT 5;

-- 4. فحص الحقول المحددة في طلبات الصلاحيات
-- Check specific fields in permission requests
SELECT 
    br.id,
    br.name,
    br.accounting_dept,
    br.accounting_level,
    br.internal_audit,
    br.risk_dept,
    br.operations_dept,
    br.transaction_amount_limit
FROM bssic_request br
JOIN bssic_request_type brt ON br.request_type_id = brt.id
WHERE brt.code = 'permission'
AND br.id IN (
    SELECT id FROM bssic_request 
    WHERE request_type_id IN (
        SELECT id FROM bssic_request_type WHERE code = 'permission'
    )
    ORDER BY id DESC 
    LIMIT 3
);

-- 5. إصلاح نوع الطلب إذا لزم الأمر
-- Fix request type if needed
UPDATE bssic_request_type 
SET 
    show_permission_fields = true,
    active = true
WHERE code = 'permission' 
AND (show_permission_fields = false OR active = false);

-- 6. التحقق من النتيجة بعد الإصلاح
-- Verify result after fix
SELECT 
    'After Fix:' as status,
    id,
    name,
    code,
    active,
    show_permission_fields
FROM bssic_request_type 
WHERE code = 'permission';
