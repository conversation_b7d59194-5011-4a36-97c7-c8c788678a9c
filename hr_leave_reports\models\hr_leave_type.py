# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models


class HrLeaveType(models.Model):
    _inherit = 'hr.leave.type'

    auto_generate_report = fields.Boolean(
        string='Auto Generate Report on Approval',
        default=True,
        help='Automatically generate and send leave report to employee when leave is approved'
    )


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # إضافة الحقل كحل احتياطي في حالة عدم وجوده في مديول آخر
    int_id = fields.Char(
        string='Employee ID',
        help='Internal Employee ID'
    )
