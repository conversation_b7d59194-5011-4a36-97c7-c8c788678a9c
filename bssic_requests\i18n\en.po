# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* bssic_requests
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-20 12:00+0000\n"
"PO-Revision-Date: 2024-12-20 12:00+0000\n"
"Last-Translator: BSIC Team\n"
"Language-Team: English\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: bssic_requests
#: code:addons/bssic_requests/models/base_request.py:14
msgid "New"
msgstr "New"

#. module: bssic_requests
#: model:ir.module.category,name:bssic_requests.module_category_bssic_requests
msgid "BSIC Requests"
msgstr "BSIC Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_root
msgid "BSIC Requests"
msgstr "BSIC Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_request
msgid "Requests"
msgstr "Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_stationery
msgid "Stationery"
msgstr "Stationery"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_my_request
msgid "My Requests"
msgstr "My Requests"

#. module: bssic_requests
#: model_terms:ir.ui.menu,name:bssic_requests.menu_bssic_all_request
msgid "All Requests"
msgstr "All Requests"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_password_reset
msgid "Password Reset"
msgstr "Password Reset"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_usb
msgid "USB Access"
msgstr "USB Access"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_extension
msgid "Extension Request"
msgstr "Extension Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_permission
msgid "Permission Request"
msgstr "Permission Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_email
msgid "Email Request"
msgstr "Email Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_technical
msgid "Technical Request"
msgstr "Technical Request"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_authorization_delegation
msgid "Authorization Delegation"
msgstr "Authorization Delegation"

#. module: bssic_requests
#: model:bssic.request.type,name:bssic_requests.request_type_free_entry
msgid "Free Form"
msgstr "Free Form"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__name
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__name
msgid "Request Reference"
msgstr "Request Reference"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__employee_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__employee_id
msgid "Employee"
msgstr "Employee"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__employee_number
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__employee_number
msgid "Employee Number (ID)"
msgstr "Employee Number (ID)"

#. module: bssic_requests
#: model:ir.model.fields,help:bssic_requests.field_bssic_base_request__employee_number
#: model:ir.model.fields,help:bssic_requests.field_bssic_request__employee_number
msgid "Enter employee ID number to automatically fetch employee details"
msgstr "Enter employee ID number to automatically fetch employee details"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__department_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__department_id
msgid "Department"
msgstr "Department"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__job_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__job_id
msgid "Job Position"
msgstr "Job Position"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__request_type_id
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__request_type_id
msgid "Request Type"
msgstr "Request Type"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__request_type_code
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__request_type_code
msgid "Request Type Code"
msgstr "Request Type Code"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__request_date
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__request_date
msgid "Request Date"
msgstr "Request Date"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__description
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__description
msgid "Description"
msgstr "Description"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__state
msgid "Status"
msgstr "Status"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Draft"
msgstr "Draft"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Submitted"
msgstr "Submitted"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Direct Manager Approval"
msgstr "Direct Manager Approval"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Audit Manager Approval"
msgstr "Audit Manager Approval"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "IT Manager Approval"
msgstr "IT Manager Approval"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Assigned to IT Staff"
msgstr "Assigned to IT Staff"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "In Progress"
msgstr "In Progress"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Completed"
msgstr "Completed"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__state
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__state
msgid "Rejected"
msgstr "Rejected"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Submit"
msgstr "Submit"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (Direct Manager)"
msgstr "Approve (Direct Manager)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (Audit Manager)"
msgstr "Approve (Audit Manager)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Approve (IT Manager)"
msgstr "Approve (IT Manager)"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Assign to IT Staff"
msgstr "Assign to IT Staff"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Mark In Progress"
msgstr "Mark In Progress"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Mark Completed"
msgstr "Mark Completed"

#. module: bssic_requests
#: model_terms:ir.ui.view,arch_db:bssic_requests.view_bssic_request_form
msgid "Reject"
msgstr "Reject"

#. module: bssic_requests
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,field_description:bssic_requests.field_bssic_request__priority
msgid "Priority"
msgstr "Priority"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Low"
msgstr "Low"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Normal"
msgstr "Normal"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "High"
msgstr "High"

#. module: bssic_requests
#: model:ir.model.fields,selection:bssic_requests.field_bssic_base_request__priority
#: model:ir.model.fields,selection:bssic_requests.field_bssic_request__priority
msgid "Urgent"
msgstr "Urgent"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_employee
msgid "Employee"
msgstr "Employee"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_direct_manager
msgid "Direct Manager"
msgstr "Direct Manager"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_audit_manager
msgid "Audit Manager"
msgstr "Audit Manager"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_it_manager
msgid "IT Manager"
msgstr "IT Manager"

#. module: bssic_requests
#: model:res.groups,name:bssic_requests.group_bssic_it_staff
msgid "IT Staff"
msgstr "IT Staff"
