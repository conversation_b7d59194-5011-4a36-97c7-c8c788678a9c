# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from datetime import datetime
from odoo.exceptions import ValidationError


class HrLeaveReportWizard(models.TransientModel):
    _name = 'hr.leave.report.wizard'
    _description = 'تقرير الإجازات'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=fields.Date.context_today
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.context_today
    )
    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee'
    )
    all_employees = fields.Boolean(
        string='All Employees',
        default=True
    )
    state_filter = fields.Selection([
        ('all', 'All States'),
        ('draft', 'Draft'),
        ('confirm', 'To Approve'),
        ('validate1', 'Second Approval'),
        ('validate', 'Approved'),
        ('refuse', 'Refused'),
        ('cancel', 'Cancelled')
    ], string='Leave State', default='all')

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for record in self:
            if record.date_from > record.date_to:
                raise ValidationError('Start date must be before end date')

    @api.onchange('all_employees')
    def _onchange_all_employees(self):
        if self.all_employees:
            self.employee_id = False

    def get_leave_data(self):
        """جمع بيانات الإجازات حسب المعايير المحددة"""
        domain = [
            ('date_from', '>=', self.date_from),
            ('date_to', '<=', self.date_to),
        ]

        # تصفية حسب الموظف
        if not self.all_employees and self.employee_id:
            domain.append(('employee_id', '=', self.employee_id.id))

        # تصفية حسب الحالة
        if self.state_filter != 'all':
            domain.append(('state', '=', self.state_filter))

        leaves = self.env['hr.leave'].search(domain, order='employee_id, date_from')

        leave_data = []
        for leave in leaves:
            # تحويل التواريخ إلى التوقيت المحلي
            date_from_local = leave.date_from.date() if leave.date_from else False
            date_to_local = leave.date_to.date() if leave.date_to else False

            # ترجمة حالة الإجازة
            state_translation = {
                'draft': 'Draft',
                'confirm': 'To Approve',
                'validate1': 'Second Approval',
                'validate': 'Approved',
                'refuse': 'Refused',
                'cancel': 'Cancelled'
            }

            # الحصول على الرقم الوظيفي الصحيح
            employee_code = ''
            if hasattr(leave.employee_id, 'int_id') and leave.employee_id.int_id:
                employee_code = leave.employee_id.int_id
            elif hasattr(leave.employee_id, 'identification_id') and leave.employee_id.identification_id:
                employee_code = leave.employee_id.identification_id
            elif hasattr(leave.employee_id, 'barcode') and leave.employee_id.barcode:
                employee_code = leave.employee_id.barcode
            else:
                employee_code = str(leave.employee_id.id)

            leave_data.append({
                'employee_code': employee_code,
                'employee_name': leave.employee_id.name or '',
                'department': leave.department_id.name or '',
                'leave_type': leave.holiday_status_id.name or '',
                'date_from': date_from_local,
                'date_to': date_to_local,
                'number_of_days': leave.number_of_days or 0,
                'state': state_translation.get(leave.state, leave.state),
                'description': leave.name or '',
                'request_date': leave.create_date.date() if leave.create_date else False,
                # معلومات الموافقة
                'first_approver': leave.first_approver_id.name if leave.first_approver_id else '',
                'second_approver': leave.second_approver_id.name if leave.second_approver_id else '',
                'validation_type': leave.validation_type or ''
            })

        return leave_data

    def get_employee_leave_balance(self, employee_id, leave_type_id):
        """الحصول على رصيد الإجازات للموظف المحدد"""
        if not employee_id or not leave_type_id:
            return {
                'balance_before': 0,
                'requested_days': 0,
                'balance_after': 0,
                'leave_type_name': ''
            }

        # الحصول على نوع الإجازة
        leave_type = self.env['hr.leave.type'].browse(leave_type_id)

        # الحصول على بيانات الإجازات للموظف
        employee_days = leave_type.with_context(employee_id=employee_id).get_employees_days([employee_id])
        employee_data = employee_days.get(employee_id, {})
        leave_type_data = employee_data.get(leave_type_id, {})

        # حساب الأيام المطلوبة في الفترة المحددة
        domain = [
            ('employee_id', '=', employee_id),
            ('holiday_status_id', '=', leave_type_id),
            ('date_from', '>=', self.date_from),
            ('date_to', '<=', self.date_to),
            ('state', '=', 'validate')
        ]

        leaves_in_period = self.env['hr.leave'].search(domain)
        requested_days = sum(leave.number_of_days for leave in leaves_in_period)

        # الرصيد الحالي (بعد جميع الإجازات)
        current_balance = leave_type_data.get('remaining_leaves', 0)

        # الرصيد قبل طلب الإجازة (إضافة الأيام المطلوبة للرصيد الحالي)
        balance_before = current_balance + requested_days

        return {
            'balance_before': balance_before,
            'requested_days': requested_days,
            'balance_after': current_balance,
            'leave_type_name': leave_type.name,
            'max_leaves': leave_type_data.get('max_leaves', 0),
            'leaves_taken': leave_type_data.get('leaves_taken', 0)
        }

    def get_employee_approval_info(self, employee_id):
        """الحصول على معلومات الموافقة للموظف المحدد"""
        if not employee_id:
            return {
                'show_approval': False,
                'approvals': []
            }

        # البحث عن الإجازات الموافق عليها للموظف في الفترة المحددة
        domain = [
            ('employee_id', '=', employee_id),
            ('date_from', '>=', self.date_from),
            ('date_to', '<=', self.date_to),
            ('state', '=', 'validate')
        ]

        leaves = self.env['hr.leave'].search(domain, order='date_from desc')

        if not leaves:
            return {
                'show_approval': False,
                'approvals': []
            }

        approvals = []
        for leave in leaves:
            approval_info = {
                'leave_type': leave.holiday_status_id.name,
                'date_from': leave.date_from.date() if leave.date_from else '',
                'date_to': leave.date_to.date() if leave.date_to else '',
                'number_of_days': leave.number_of_days,
                'validation_type': leave.validation_type,
                'first_approver': '',
                'second_approver': '',
                'approved_by': '',
                'validated_by': ''
            }

            # تحديد من قام بالموافقة حسب نوع التحقق
            if leave.validation_type == 'manager':
                # موافقة مدير واحد فقط
                if leave.first_approver_id:
                    approval_info['approved_by'] = leave.first_approver_id.name
                    approval_info['first_approver'] = leave.first_approver_id.name
            elif leave.validation_type == 'both':
                # موافقة مزدوجة
                if leave.first_approver_id:
                    approval_info['approved_by'] = leave.first_approver_id.name
                    approval_info['first_approver'] = leave.first_approver_id.name
                if leave.second_approver_id:
                    approval_info['validated_by'] = leave.second_approver_id.name
                    approval_info['second_approver'] = leave.second_approver_id.name
            elif leave.validation_type == 'hr':
                # موافقة HR فقط
                if leave.first_approver_id:
                    approval_info['validated_by'] = leave.first_approver_id.name
                    approval_info['first_approver'] = leave.first_approver_id.name

            approvals.append(approval_info)

        return {
            'show_approval': True,
            'approvals': approvals,
            'total_approvals': len(approvals)
        }

    def generate_report(self):
        """إنشاء التقرير"""
        if not self.date_from or not self.date_to:
            raise ValidationError('Start and end dates must be specified')

        leave_data = self.get_leave_data()

        # تنسيق تاريخ التقرير
        current_datetime = fields.Datetime.now()
        formatted_date = current_datetime.strftime('%Y-%m-%d %H:%M') if current_datetime else ''

        # إعداد البيانات الأساسية
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'employee_name': self.employee_id.name if self.employee_id else 'All Employees',
            'all_employees': self.all_employees,
            'state_filter': dict(self._fields['state_filter'].selection).get(self.state_filter),
            'leave_data': leave_data,
            'total_leaves': len(leave_data),
            'total_days': sum(leave['number_of_days'] for leave in leave_data),
            'report_date': formatted_date,
            'show_balance': False,  # افتراضياً لا نعرض الرصيد
            'balance_info': {},
            'show_approval': False,  # افتراضياً لا نعرض معلومات الموافقة
            'approval_info': {}
        }

        # إضافة معلومات الرصيد إذا كان التقرير لموظف محدد
        if not self.all_employees and self.employee_id and leave_data:
            # الحصول على نوع الإجازة الأكثر استخداماً في الفترة
            leave_types_count = {}
            for leave in leave_data:
                leave_type = leave['leave_type']
                if leave_type in leave_types_count:
                    leave_types_count[leave_type] += leave['number_of_days']
                else:
                    leave_types_count[leave_type] = leave['number_of_days']

            if leave_types_count:
                # أخذ نوع الإجازة الأكثر استخداماً
                most_used_leave_type = max(leave_types_count, key=leave_types_count.get)

                # البحث عن ID نوع الإجازة
                leave_type_record = self.env['hr.leave.type'].search([('name', '=', most_used_leave_type)], limit=1)

                if leave_type_record:
                    balance_info = self.get_employee_leave_balance(self.employee_id.id, leave_type_record.id)
                    data['show_balance'] = True
                    data['balance_info'] = balance_info

            # إضافة معلومات الموافقة للموظف المحدد
            approval_info = self.get_employee_approval_info(self.employee_id.id)
            data['show_approval'] = approval_info['show_approval']
            data['approval_info'] = approval_info

        return self.env.ref('hr_leave_reports.hr_leave_report_pdf').report_action(self, data=data)
