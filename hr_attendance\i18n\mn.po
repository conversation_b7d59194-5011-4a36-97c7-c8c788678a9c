# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# Baskhu<PERSON> <baskhu<PERSON><PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>aa, 2021
# <PERSON>, 2021
# Ganbaata<PERSON> Buriad <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Khoschu<PERSON>u <PERSON>luu <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "\"Гарсан\" цаг \"Орсон\" цагаас эрт байж болохгүй."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr "%(empl_name)s %(check_in)s-с"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr "%(empl_name)s %(check_in)s-с %(check_out)sхүртэл"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr ""
"<b>Сануулга! Хамгийн сүүлд, Орсон цаг 12 цагийн ѳмнѳ байна.</b><br/>Хэрвээ "
"та үүнийг буруу гэж үзэж байвал Хүний нѳѳцийн ажилтанд хандана уу."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Мѳчлѳг\" role=\"img\" title=\"Мѳчлѳг\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Employee PIN</span>"
msgstr "<span class=\"o_form_label\">Ажилтны ПИН</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Month\n"
"                        </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attendance</span>"
msgstr "<span class=\"o_stat_text\">Ирц</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attended Since</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Not Attended Since</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle "
"oe_hr_attendance_status_green\" role=\"img\" aria-label=\"Available\" "
"title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle "
"oe_hr_attendance_status_green\" role=\"img\" aria-label=\"Боломжтой\" "
"title=\"Боломжтой\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle oe_hr_attendance_status_orange\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                    <br/>\n"
"                                    <br/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Эндээс та ажилтны Орсон / Гарсан бүртгэлийг оруулахын тулд цѳѳн тооны ажилчдыг сонгох боломжтой юм.\n"
"Хэрэв та шинээр ажилтан үүсгэх бол Ажилчид цэс үрүү орно уу."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "Зөрүү"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr "Өдөрт нэг алим идсэнээр эмнэлэг зүглэх шаардлагагүй болно"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Another good day's work! See you soon!"
msgstr "Бас нэгэн ажлын сайхан өдөр! Дараа уулзая!"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_pivot
msgid "Attendance"
msgstr "Ирц"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_report_action_filtered
msgid "Attendance Analysis"
msgstr "Ирцийн шинжилгээ"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_report
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_graph
msgid "Attendance Statistics"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "Ирцийн статус"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_overview
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_attendances_overview
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Ирц"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#, python-format
msgid "Available"
msgstr "Идэвхтэй"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"%(empl_name)s ажилтанд шинэ ирж үүсгэх боломжгүй, ажилтан нь %(datetime)s-с "
"хойш гараагүй."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"%(empl_name)s ажилтанд ирж үүсгэх боломжгүй, ажилтан нь өмнө %(datetime)s-д "
"аль хэдийнээ орсон байна"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"%(empl_name)s дээр гарсан үйлдэл бүртгэх боломжгүй, харгалзах орсон үйлдэл "
"алга. Таны ирцийг хүний нөөцөөс засварлаж байж болох юм."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "Орсон"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "Орсон / Гарсан"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "Гарсан"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr "Орох/Гарах"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Орсон"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "Орсон"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Гарсан"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "Гарсан"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Click to"
msgstr "Дараад"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "Компани"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Company Logo"
msgstr "Компанийн лого"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Тохиргоо"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "Шинээр ажилтан үүсгэх"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "Хоног"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__department_id
msgid "Department"
msgstr "Алба нэгж"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr "Эрт унтаж эрт босох нь хүний эрүүл, чийрэг, сэргэлэн байх үндэс"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr ""
"Өглөөний цайг хаан шиг, үдийн цайг наймаачин шиг, оройн хоолыг гуйлгачин шиг"
" ид. "

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "Ажилтан"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr "Ажилтны PIN"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "Ажилчдын ирц"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "Хүний нөөц"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr "PIN идэвхжүүлэх"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "Алдаа: харгалзах ажилтанг олсонгүй."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "First come, first served"
msgstr "Түрүүлж ирвэл түрүүлж үйлчлүүлнэ"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr "Таны эргэн ирсэн баяртай байна, нэлээд уджээ!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Go back"
msgstr "Буцах"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good afternoon"
msgstr "Өдрийн мэнд"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good evening"
msgstr "Оройн мэнд"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good morning"
msgstr "Өглөөний мэнд"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good night"
msgstr "Сайхан амраарай"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Goodbye"
msgstr "Баяртай"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_report_view_search
msgid "HR Attendance Search"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good afternoon"
msgstr "Үдийг сайхан өнгөрүүлээрэй"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good day!"
msgstr "Өдрийг сайхан өнгөрүүлээрэй"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good evening"
msgstr "Оройг сайхан өнгөрүүлээрэй"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a nice lunch!"
msgstr "Сайхан хооллоорой!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Цаг"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__worked_hours
msgid "Hours Worked"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Ирцээс хайх"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Identify Manually"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr "Хэрэв ажил нь хийх үнэ цэнэтэй бол энэ нь сайн хийх үнэ цэнэтэй!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Invalid request, please return to the main menu."
msgstr "Буруу хүсэлт, үндсэн меню рүү буцна уу."

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_kiosk
msgid "Kiosk Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "Kiosk Mode"
msgstr "Киоск горим"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Сүүлийн Ирц"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_report____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "Manual Attendance"
msgstr "Гар Ирц"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_ir_ui_menu
msgid "Menu"
msgstr "Цэс"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Зурвас"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "Миний ирцүүд"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "Гарсан алга"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr "Ирцийн бүртгэл олдсонгүй"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "No attendance records to display"
msgstr "Дэлгэцэд харуулах ирцийн бүртгэл байхгүй байна"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Not available"
msgstr "Идэвхгүй"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer"
msgstr "Мэргэжилтэн"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
msgid "Overtime"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "ПИН код оруулна уу. Нэвтрэх горим: "

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "Үндсэн меню рүү буцна уу."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "Ажилтны нээлттэй мэдээлэл"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "Тайлан"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Үнэмлэхээ уншуулна уу"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""
"Ажилтны хувийн хэргийн бүртгэл дэх (HR тохиргоо) цэсээр орон ПИН тохируулах."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Тохиргоо"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign in"
msgstr "Нэвтрэх"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign out"
msgstr "Гарах"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "Эхлэх"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "Ийм бүлэглэлийг зѳвшѳѳрѳхгүй."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr "Танай ажилтнуудын ирцүүд энэ дэлгэцэнд энд харагдана."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "The early bird catches the worm"
msgstr "Эртэч шувууд өтийг барьдаг"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_kiosk
msgid ""
"The user will be able to open the kiosk mode and validate the employee PIN."
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr ""
"Хэрэглэгч нь хүний нөөцийн ирц меню рүү хандах эрхтэй болох бөгөөд хэрэглэгч"
" өөрийн ирцийг удирдах боломжтой болно."

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr ""
"Хэрэглэгч нь орсон гарснаа бүртгүүлэхдээ гараараа дэлгэц дээр PIN кодыг "
"оруулах ёстой."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"To activate Kiosk mode without pin code, you must have access right as an "
"Officer or above in the Attendance app. Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Today's work hours:"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Total extra hours:"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr "Киоск Горимоор ПИН код ашиглан орох"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "Users"
msgstr "Хэрэглэгчид"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr "Та гармаар байна уу?"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid ""
"Warning : Your user should be linked to an employee to use attendance. "
"Please contact your administrator."
msgstr ""
"Сануулга: Ирцийг ашиглахын тулд хэрэглэгчийн мэдээллийг ажилтны мэдээлэлтэй "
"холбох шаардлагатай юм. Та Администратор-той холбогдоно уу."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome"
msgstr "Тавтай морилно уу"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome to"
msgstr "Тавтай морил"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome!"
msgstr "Тавтай морилно уу!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "Ажлын цаг"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr "Ажилласан цаг"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours last month"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Wrong PIN"
msgstr "Буруу PIN"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "Ирцийг хувилах боломжгүй."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_overview
msgid "Your attendance records will be displayed here."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr "oрох"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr "гарах"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_ids
msgid "list of attendances for the employee"
msgstr "ажилтны ирцийн жагсаалт"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "or"
msgstr "эсвэл"
