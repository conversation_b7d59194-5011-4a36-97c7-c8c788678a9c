# إصلاح حساب Years in Current Position

## 🐛 **المشكلة:**

حقل "Years in Current Position" لا يعمل بشكل صحيح إلا بعد إضافة حدث وظيفي ثاني.

## 🔍 **سبب المشكلة:**

### المنطق القديم (الخطأ):
```python
@api.depends('last_career_event_date', 'create_date')
def _compute_years_in_position(self):
    for employee in self:
        if employee.last_career_event_date:
            start_date = employee.last_career_event_date  # خطأ!
        else:
            start_date = employee.create_date.date()
```

### المشكلة في السيناريوهات:

#### السيناريو 1: لا توجد أحداث وظيفية
- ✅ **يعمل بشكل صحيح**: يحسب من `create_date`

#### السيناريو 2: حدث وظيفي واحد
- ❌ **خطأ**: يحسب من تاريخ الحدث بدلاً من اليوم
- **المثال**: حدث منذ سنة → يظهر سنة واحدة بدلاً من صفر

#### السيناريو 3: حدثين أو أكثر
- ✅ **يعمل بشكل صحيح**: يحسب من آخر حدث

## ✅ **الحل المطبق:**

### المنطق الجديد (الصحيح):
```python
@api.depends('career_progression_ids.event_date', 'career_progression_ids.state', 'create_date')
def _compute_years_in_position(self):
    for employee in self:
        # Get the most recent confirmed career event
        last_confirmed_event = employee.career_progression_ids.filtered(
            lambda x: x.state == 'confirmed'
        ).sorted('event_date', reverse=True)[:1]
        
        if last_confirmed_event:
            # Start counting from the date of the last confirmed career event
            start_date = last_confirmed_event.event_date
        else:
            # If no career events, start from employee creation date
            start_date = employee.create_date.date() if employee.create_date else fields.Date.today()

        today = fields.Date.today()
        years = (today - start_date).days / 365.25
        employee.years_in_current_position = round(years, 1)
```

## 🔄 **التحسينات المطبقة:**

### 1. **Dependencies محدثة:**
- ✅ `career_progression_ids.event_date`
- ✅ `career_progression_ids.state`
- ✅ `create_date`

### 2. **منطق محسن:**
- ✅ يعتبر فقط الأحداث المؤكدة (`state == 'confirmed'`)
- ✅ يأخذ آخر حدث مؤكد بالترتيب الزمني
- ✅ يحسب من تاريخ الحدث إلى اليوم

### 3. **إصلاحات إضافية:**
- ✅ إصلاح مرجع `new_job_title` → `new_job_id.name`
- ✅ إزالة مرجع `previous_work_location_id`
- ✅ تحديث الاختبارات

## 📋 **أمثلة عملية:**

### المثال الأول: موظف جديد بدون أحداث
```
Employee created: 2024-01-01
Today: 2024-01-15
Career events: None

Result: 0.0 years (من تاريخ الإنشاء)
```

### المثال الثاني: موظف مع حدث واحد
```
Employee created: 2023-01-01
Career event: 2023-06-01 (Promotion)
Today: 2024-01-01

Result: 0.6 years (من تاريخ الترقية إلى اليوم)
```

### المثال الثالث: موظف مع أحداث متعددة
```
Employee created: 2022-01-01
Event 1: 2022-06-01 (Promotion)
Event 2: 2023-03-01 (Transfer)
Today: 2024-01-01

Result: 0.8 years (من آخر حدث إلى اليوم)
```

## 🧪 **الاختبارات المحدثة:**

### اختبار شامل للسيناريوهات الثلاثة:
```python
def test_years_in_current_position(self):
    # Test 1: No career events
    self.employee._compute_years_in_position()
    self.assertLess(self.employee.years_in_current_position, 0.1)
    
    # Test 2: One career event (2 years ago)
    old_event = self.env['career.progression'].create({...})
    self.employee._compute_years_in_position()
    self.assertGreater(self.employee.years_in_current_position, 1.9)
    
    # Test 3: More recent event (1 year ago)
    recent_event = self.env['career.progression'].create({...})
    self.employee._compute_years_in_position()
    self.assertGreater(self.employee.years_in_current_position, 0.9)
```

## 🎯 **النتائج:**

### قبل الإصلاح:
- ❌ حدث واحد: يظهر سنوات خاطئة
- ❌ يحتاج حدث ثاني ليعمل بشكل صحيح
- ❌ منطق مربك ومعقد

### بعد الإصلاح:
- ✅ يعمل مع حدث واحد بشكل صحيح
- ✅ يعمل مع أحداث متعددة بشكل صحيح
- ✅ يعمل بدون أحداث بشكل صحيح
- ✅ منطق واضح ومفهوم

## 🔧 **للمطورين:**

### تشغيل الاختبارات:
```bash
# اختبار حساب السنوات
python -m pytest career_progression/tests/test_career_progression.py::TestCareerProgression::test_years_in_current_position
```

### إعادة حساب يدوي:
```python
# من Odoo shell
employee = self.env['hr.employee'].browse(employee_id)
employee._compute_years_in_position()
print(f"Years in position: {employee.years_in_current_position}")
```

## ⚠️ **ملاحظات مهمة:**

### 1. **الأحداث المؤكدة فقط:**
- يعتبر فقط الأحداث في حالة `confirmed`
- الأحداث في حالة `draft` أو `cancelled` لا تؤثر

### 2. **الترتيب الزمني:**
- يأخذ آخر حدث مؤكد حسب التاريخ
- إذا كان هناك أحداث في نفس التاريخ، يأخذ آخر واحد تم إنشاؤه

### 3. **الدقة:**
- يحسب بدقة يوم واحد
- يقرب النتيجة إلى منزلة عشرية واحدة

## 🎉 **النتيجة:**

الآن حقل "Years in Current Position" يعمل بشكل صحيح من الحدث الأول، ويعطي نتائج دقيقة ومنطقية في جميع السيناريوهات!
