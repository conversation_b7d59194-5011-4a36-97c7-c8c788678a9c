/* Career Progression Module Styles */

/* Career Path Tab Styling */
.o_career_progression_header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.o_career_progression_header h3 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0;
}

/* Career Summary Cards */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* Career Events List Styling */
.o_career_progression_list .o_list_view {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.o_career_progression_list .o_list_view th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.o_career_progression_list .o_list_view td {
    vertical-align: middle;
    padding: 12px 8px;
}

/* Event Type Badges */
.o_career_progression_list .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Quick Actions Styling */
.o_career_quick_actions .alert {
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-left: 4px solid #17a2b8;
}

.o_career_quick_actions .alert-heading {
    color: #0c5460;
    font-weight: 600;
}

.o_career_quick_actions .btn {
    border-radius: 20px;
    font-weight: 500;
    padding: 0.375rem 1rem;
    transition: all 0.2s ease-in-out;
}

.o_career_quick_actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Timeline-like styling for career events */
.o_career_timeline_item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.o_career_timeline_item::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: -20px;
    width: 2px;
    background: #dee2e6;
}

.o_career_timeline_item::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.o_career_timeline_item.promotion::after {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.o_career_timeline_item.transfer::after {
    background: #ffc107;
    box-shadow: 0 0 0 2px #ffc107;
}

.o_career_timeline_item.temporary_assignment::after {
    background: #17a2b8;
    box-shadow: 0 0 0 2px #17a2b8;
}

/* Form View Enhancements */
.o_form_view .oe_title h1 {
    color: #495057;
    font-weight: 600;
}

.o_form_view .o_group .o_form_label {
    font-weight: 500;
    color: #6c757d;
}

/* Button Box Styling */
.oe_button_box .oe_stat_button {
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.oe_button_box .oe_stat_button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Kanban View Enhancements */
.o_kanban_view .o_kanban_record {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease-in-out;
}

.o_kanban_view .o_kanban_record:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Status Bar Styling */
.o_form_statusbar .o_statusbar_status {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_career_progression_header {
        padding: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .card h2 {
        font-size: 1.5rem;
    }
    
    .o_career_quick_actions .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .o_career_progression_header,
    .o_career_quick_actions {
        background: none !important;
        box-shadow: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
