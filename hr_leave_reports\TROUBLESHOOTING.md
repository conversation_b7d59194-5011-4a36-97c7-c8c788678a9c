# دليل استكشاف أخطاء التحميل الفوري للتقرير

## المشكلة: عدم تحميل التقرير فوراً عند الضغط على Validate

### الخطوات التشخيصية:

## 1. التحقق من إعدادات نوع الإجازة

### الخطوة الأولى: فحص إعدادات نوع الإجازة
1. اذهب إلى **الإجازات** → **التكوين** → **أنواع الإجازات**
2. افتح نوع الإجازة المطلوب
3. تأكد من أن حقل **"Auto Generate Report on Approval"** مفعل ✅

### إذا لم يظهر الحقل:
```bash
# أعد تشغيل Odoo مع تحديث المديول
./odoo-bin -d your_database -u hr_leave_reports
```

## 2. فحص سجلات النظام

### تفعيل وضع التطوير:
1. اذهب إلى **الإعدادات** → **تفعيل وضع المطور**
2. أو أضف `?debug=1` إلى رابط الصفحة

### فحص سجلات Odoo:
```bash
# مراقبة السجلات في الوقت الفعلي
tail -f /var/log/odoo/odoo.log | grep -i "leave\|report"

# أو فحص آخر 100 سطر
tail -100 /var/log/odoo/odoo.log | grep -i "leave\|report"
```

### البحث عن رسائل التشخيص:
- `"=== بدء عملية الموافقة على الإجازة ==="`
- `"فحص الإجازة للموظف"`
- `"بدء تحميل التقرير فوراً"`
- `"=== بدء تحميل التقرير فوراً ==="`
- `"تم تحضير التقرير للتحميل الفوري"`

## 3. اختبار يدوي من Odoo Shell

### تشغيل اختبار التشخيص:
```bash
# الدخول إلى Odoo shell
./odoo-bin shell -d your_database

# تشغيل الاختبار
exec(open('/path/to/hr_leave_reports/debug_auto_print.py').read())
```

### اختبار يدوي للتحميل الفوري:
```python
# في Odoo shell
leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
if leave:
    result = leave._download_leave_report_immediately(leave)
    print("نتيجة التحميل:", result)
    print("تحقق من سجل الإجازة للنتائج")
```

## 4. التحقق من المرفقات والرسائل

### فحص سجل الإجازة:
1. افتح الإجازة الموافق عليها
2. تحقق من وجود رسائل في سجل الإجازة
3. ابحث عن رسائل مثل:
   - `"تم إنشاء تقرير الإجازة وتحميله تلقائياً"`
   - `"خطأ في تحميل تقرير الإجازة فوراً"`

### التحقق من التحميل الفوري:
1. عند الضغط على **Validate** يجب أن يبدأ تحميل ملف PDF فوراً
2. إذا لم يحدث تحميل، تحقق من سجل الإجازة للأخطاء
3. تأكد من أن المتصفح لا يحجب التحميلات التلقائية

## 5. الحلول الشائعة

### الحل 1: تحديث المديول
```bash
./odoo-bin -d your_database -u hr_leave_reports --stop-after-init
```

### الحل 2: إعادة تثبيت المديول
```bash
# إلغاء التثبيت
./odoo-bin -d your_database --uninstall hr_leave_reports --stop-after-init

# إعادة التثبيت
./odoo-bin -d your_database -i hr_leave_reports --stop-after-init
```

### الحل 3: تفعيل التقرير التلقائي لجميع أنواع الإجازات
```bash
# في Odoo shell
./odoo-bin shell -d your_database

# تشغيل سكريبت التفعيل
exec(open('/path/to/hr_leave_reports/enable_auto_report.py').read())
enable_auto_report_for_all_leave_types()
```

أو يدوياً:
```python
# في Odoo shell
leave_types = env['hr.leave.type'].search([])
for leave_type in leave_types:
    if hasattr(leave_type, 'auto_generate_report'):
        leave_type.auto_generate_report = True
env.cr.commit()
```

### الحل 4: فحص الصلاحيات
```python
# في Odoo shell
user = env.user
print(f"المستخدم: {user.name}")
print(f"المجموعات: {[g.name for g in user.groups_id]}")

# التحقق من صلاحيات إنشاء المرفقات
try:
    env['ir.attachment'].check_access_rights('create')
    print("✓ صلاحيات إنشاء المرفقات متوفرة")
except:
    print("✗ لا توجد صلاحيات لإنشاء المرفقات")
```

## 6. رسائل الخطأ الشائعة وحلولها

### خطأ: "نموذج hr.leave.report.wizard غير موجود"
**الحل:** تأكد من تثبيت المديول بشكل صحيح
```bash
./odoo-bin -d your_database -u hr_leave_reports
```

### خطأ: "مرجع التقرير غير موجود"
**الحل:** تحقق من ملف `report/hr_leave_report_config.xml`
```bash
grep -r "hr_leave_report_pdf" /path/to/hr_leave_reports/
```

### خطأ: "لا توجد بيانات للتقرير"
**الحل:** تحقق من فلاتر التقرير في دالة `get_leave_data()`

## 7. اختبار شامل

### إنشاء إجازة اختبار:
1. أنشئ إجازة جديدة لموظف
2. اضغط **Submit to Manager**
3. اضغط **Validate**
4. تحقق من:
   - بدء تحميل ملف PDF فوراً
   - ظهور رسالة في سجل الإجازة
   - عدم إرسال إيميل للموظف (التحميل الفوري فقط)

## 8. مشكلة الرقم الوظيفي غير الصحيح

### المشكلة: الرقم الوظيفي المعروض في التقرير غير صحيح

### التشخيص:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_employee_id.py').read())
test_employee_id_fields()
```

### الحلول:

#### الحل 1: تعيين أرقام وظيفية للموظفين
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_employee_id.py').read())
set_employee_int_id()
```

#### الحل 2: تحديث الرقم الوظيفي يدوياً
1. اذهب إلى **الموظفين** → **الموظفين**
2. افتح بيانات الموظف
3. في تبويب **معلومات العمل**، املأ حقل **Employee ID**

#### الحل 3: فحص أولوية الحقول
التقرير يستخدم الحقول بهذا الترتيب:
1. `int_id` (Employee ID)
2. `identification_id` (Identification No)
3. `barcode` (Badge ID)
4. `id` (Database ID)

### اختبار الرقم الوظيفي في التقرير:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_employee_id.py').read())
test_leave_report_employee_code()
```

## 9. معلومات الرصيد في التقرير

### المشكلة: لا تظهر معلومات الرصيد في التقرير

### الشروط لظهور معلومات الرصيد:
1. التقرير يجب أن يكون لموظف محدد (وليس جميع الموظفين)
2. يجب أن توجد إجازات للموظف في الفترة المحددة
3. يجب أن يكون لنوع الإجازة تخصيصات (allocations)

### التشخيص:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_balance_info.py').read())
test_employee_balance()
```

### الحلول:

#### الحل 1: التحقق من تخصيصات الإجازات
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_balance_info.py').read())
create_test_allocation()
```

#### الحل 2: إنشاء تخصيصات يدوياً
1. اذهب إلى **الإجازات** → **التخصيصات** → **تخصيصات الإجازات**
2. اضغط **إنشاء**
3. اختر الموظف ونوع الإجازة
4. حدد عدد الأيام
5. اضغط **موافقة**

#### الحل 3: اختبار حساب الرصيد
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_balance_info.py').read())
test_balance_calculation()
```

### اختبار التقرير مع الرصيد:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_balance_info.py').read())
test_report_with_balance()
```

## 10. معلومات الموافقة في التقرير

### المشكلة: لا تظهر معلومات الموافقة في التقرير

### الشروط لظهور معلومات الموافقة:
1. التقرير يجب أن يكون لموظف محدد (وليس جميع الموظفين)
2. يجب أن توجد إجازات موافق عليها للموظف في الفترة المحددة
3. يجب أن تحتوي الإجازات على معلومات الموافقة (first_approver_id, second_approver_id)

### التشخيص:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_approval_info.py').read())
test_leave_approvals()
```

### الحلول:

#### الحل 1: التحقق من معلومات الموافقة الموجودة
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_approval_info.py').read())
test_approval_info_function()
```

#### الحل 2: إنشاء إجازة اختبارية مع موافقة
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_approval_info.py').read())
create_test_leave_with_approval()
```

#### الحل 3: التحقق من أنواع التحقق
أنواع التحقق المدعومة:
- `manager`: موافقة مدير واحد (يظهر في "Approved By")
- `both`: موافقة مزدوجة (يظهر في "Approved By" و "Validated By")
- `hr`: موافقة HR (يظهر في "Validated By")
- `no_validation`: بدون موافقة (لا تظهر معلومات موافقة)

### اختبار التقرير مع معلومات الموافقة:
```bash
# في Odoo shell
exec(open('/path/to/hr_leave_reports/test_approval_info.py').read())
test_report_with_approval()
```

### معلومات الموافقة المعروضة:
- **Leave Type**: نوع الإجازة
- **Period**: فترة الإجازة
- **Days**: عدد الأيام
- **Approved By**: الشخص الذي أعطى Approve
- **Validated By**: الشخص الذي أعطى Validate

## 11. الدعم الفني

إذا استمرت المشكلة:
1. جمع سجلات الأخطاء من `/var/log/odoo/odoo.log`
2. تشغيل الاختبار التشخيصي `test_print_function.py`
3. فحص إعدادات قاعدة البيانات
4. التحقق من إصدار Odoo والمديولات المثبتة
5. اختبار الرقم الوظيفي باستخدام `test_employee_id.py`

---

**ملاحظة:** تأكد من عمل نسخة احتياطية قبل تطبيق أي حلول.
