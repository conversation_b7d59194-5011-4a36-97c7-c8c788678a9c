# HR Job Title Info

## نظرة عامة

هذا الموديول يضيف تبويبة جديدة بعنوان "Job Title Info" (معلومات المسمى الوظيفي) إلى نموذج الموظف في تطبيق الموارد البشرية في Odoo 15.

## الميزات

### التبويبة الجديدة تحتوي على:

1. **المسمى الوظيفي** (Job Title) - حقل نصي قابل للتحرير
2. **تاريخ بدء المسمى الوظيفي** (Job Start Date) - حقل تاريخ
3. **القسم المرتبط** (Related Department) - ربط بقسم معين
4. **تفاصيل المسؤوليات** (Responsibilities) - حقل نص طويل
5. **المرتبة الوظيفية** (Job Level) - قائمة منسدلة بالمستويات المهنية
6. **ملاحظات إضافية** (Additional Notes) - حقل نص طويل

### المستويات المهنية المتاحة:
- مستوى مبتدئ (Entry Level)
- مبتدئ (Junior)
- مستوى متوسط (Mid Level)
- أول (Senior)
- قائد فريق (Lead)
- مدير (Manager)
- مدير عام (Director)
- تنفيذي (Executive)

## التثبيت

1. انسخ مجلد `hr_job_title_info` إلى مجلد addons في Odoo
2. قم بتحديث قائمة التطبيقات
3. ابحث عن "HR Job Title Info" وقم بتثبيته

## الاستخدام

1. اذهب إلى تطبيق الموارد البشرية
2. افتح أي سجل موظف
3. ستجد تبويبة جديدة بعنوان "Job Title Info" بجانب تبويبة "HR Settings"
4. املأ المعلومات المطلوبة وقم بالحفظ

## الميزات التقنية

- **التوافق**: Odoo 15
- **الاعتمادات**: موديول hr الأساسي
- **الأمان**: صلاحيات مناسبة لمجموعات المستخدمين المختلفة
- **الترجمة**: دعم كامل للغة العربية
- **التتبع**: جميع الحقول قابلة للتتبع (tracking)
- **التحديث التلقائي**: ربط تلقائي بين الحقول ذات الصلة

## الأمان والصلاحيات

- **HR User**: قراءة وكتابة وإنشاء
- **HR Manager**: جميع الصلاحيات بما في ذلك الحذف

## التخصيص

يمكن تخصيص الموديول بسهولة لإضافة حقول جديدة أو تعديل التخطيط حسب احتياجات المؤسسة.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**ملاحظة**: هذا الموديول متوافق مع أفضل ممارسات تطوير Odoo ويستخدم التقنيات الرسمية لـ Odoo 15.
