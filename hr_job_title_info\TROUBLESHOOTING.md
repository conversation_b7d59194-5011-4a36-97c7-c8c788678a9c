# دليل استكشاف الأخطاء وإصلاحها - HR Job Title Info

## المشاكل الشائعة والحلول

### 1. خطأ في تحميل الموديول

#### المشكلة: `Module loading hr_job_title_info failed`

**الأسباب المحتملة:**
- مشكلة في ملف الأمان
- مجموعة مستخدمين غير موجودة
- خطأ في بناء الجملة في ملفات XML

**الحلول:**
1. تحقق من ملف `security/ir.model.access.csv`
2. تأكد من وجود المجموعات المرجعية:
   - `hr.group_hr_user`
   - `hr.group_hr_manager`
3. تحقق من سجل الأخطاء في Odoo

#### المشكلة: `No matching record found for external id`

**الحل:**
```bash
# تحقق من المجموعات المتاحة
SELECT name, full_name FROM res_groups WHERE name LIKE '%hr%';
```

### 2. التبويبة لا تظهر

#### المشكلة: تبويبة "Job Title Info" غير مرئية

**الأسباب المحتملة:**
- المستخدم ليس لديه صلاحيات HR
- الموديول غير مثبت بشكل صحيح
- مشكلة في ملف views

**الحلول:**
1. تحقق من صلاحيات المستخدم:
   ```python
   # في Python console
   user = self.env.user
   print(user.has_group('hr.group_hr_user'))
   ```

2. تحقق من تثبيت الموديول:
   ```sql
   SELECT name, state FROM ir_module_module WHERE name = 'hr_job_title_info';
   ```

3. أعد تحميل الصفحة (Ctrl+F5)

### 3. الحقول لا تحفظ

#### المشكلة: البيانات لا تحفظ في قاعدة البيانات

**الأسباب المحتملة:**
- مشكلة في صلاحيات الكتابة
- خطأ في تعريف الحقول
- مشكلة في قاعدة البيانات

**الحلول:**
1. تحقق من صلاحيات الكتابة:
   ```python
   # في Python console
   employee = self.env['hr.employee'].browse(1)
   employee.check_access_rights('write')
   ```

2. تحقق من وجود الحقول في قاعدة البيانات:
   ```sql
   SELECT column_name FROM information_schema.columns 
   WHERE table_name = 'hr_employee' 
   AND column_name LIKE 'job_%';
   ```

3. أعد تحديث الموديول:
   ```bash
   # من سطر الأوامر
   ./odoo-bin -u hr_job_title_info -d your_database
   ```

### 4. الترجمة العربية لا تظهر

#### المشكلة: النصوص تظهر بالإنجليزية

**الحلول:**
1. تأكد من تفعيل اللغة العربية:
   - Settings > Translations > Languages
   - فعل Arabic

2. أعد تحميل الترجمات:
   - Settings > Translations > Load a Translation
   - اختر Arabic واضغط Load

3. تحقق من ملف الترجمة:
   ```bash
   # تحقق من وجود ملف ar.po
   ls hr_job_title_info/i18n/ar.po
   ```

### 5. أخطاء في الاختبارات

#### المشكلة: فشل في اختبارات الوحدة

**الحلول:**
1. تشغيل الاختبارات يدوياً:
   ```bash
   ./odoo-bin --test-enable --stop-after-init -i hr_job_title_info -d test_db
   ```

2. تحقق من سجل الاختبارات:
   ```bash
   tail -f /var/log/odoo/odoo.log | grep test
   ```

### 6. مشاكل الأداء

#### المشكلة: بطء في تحميل نموذج الموظف

**الحلول:**
1. تحقق من الفهارس:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_hr_employee_job_level 
   ON hr_employee(job_level);
   ```

2. تحسين الاستعلامات:
   - تجنب استخدام compute fields كثيراً
   - استخدم stored=True للحقول المحسوبة

### 7. مشاكل التوافق

#### المشكلة: تعارض مع موديولات أخرى

**الحلول:**
1. تحقق من التبعيات:
   ```python
   # في Python console
   module = self.env['ir.module.module'].search([('name', '=', 'hr_job_title_info')])
   print(module.dependencies_id.mapped('name'))
   ```

2. تحقق من التعارضات في views:
   ```bash
   grep -r "hr_settings" /path/to/addons/*/views/
   ```

## أوامر مفيدة للتشخيص

### 1. تحقق من حالة الموديول
```sql
SELECT name, state, latest_version 
FROM ir_module_module 
WHERE name = 'hr_job_title_info';
```

### 2. تحقق من الحقول المضافة
```sql
SELECT name, field_description, ttype 
FROM ir_model_fields 
WHERE model = 'hr.employee' 
AND name LIKE 'job_%';
```

### 3. تحقق من المشاهدات
```sql
SELECT name, model, inherit_id 
FROM ir_ui_view 
WHERE name LIKE '%job_title_info%';
```

### 4. تحقق من الصلاحيات
```sql
SELECT name, perm_read, perm_write, perm_create, perm_unlink 
FROM ir_model_access 
WHERE name LIKE '%job_title_info%';
```

## نصائح للصيانة

### 1. النسخ الاحتياطي
```bash
# نسخ احتياطي قبل التحديث
pg_dump your_database > backup_before_update.sql
```

### 2. مراقبة الأداء
```python
# في Python console
import time
start = time.time()
employees = self.env['hr.employee'].search([])
employees.read(['job_title_custom', 'job_level'])
print(f"Time taken: {time.time() - start} seconds")
```

### 3. تنظيف البيانات
```sql
-- إزالة البيانات الفارغة
UPDATE hr_employee 
SET job_title_custom = NULL 
WHERE job_title_custom = '';
```

## الحصول على المساعدة

1. **سجل الأخطاء**: `/var/log/odoo/odoo.log`
2. **وضع التطوير**: أضف `--dev=all` عند تشغيل Odoo
3. **Python Console**: استخدم Python console في Odoo للتشخيص
4. **Database Manager**: استخدم pgAdmin أو psql للتحقق من قاعدة البيانات

---

**ملاحظة**: احتفظ بنسخة احتياطية من قاعدة البيانات قبل إجراء أي تعديلات.
