# BSIC Requests Management | إدارة طلبات BSIC

This module provides a comprehensive system for managing various types of requests in BSIC with a multi-stage approval workflow and **full bilingual support (Arabic/English)**.

يوفر هذا المديول نظاماً شاملاً لإدارة أنواع مختلفة من الطلبات في BSIC مع سير عمل متعدد المراحل للموافقة **ودعم كامل ثنائي اللغة (عربي/إنجليزي)**.

## 🌟 New Features | الميزات الجديدة

### ✅ Complete Bilingual Support | دعم ثنائي اللغة الكامل
- **Full Arabic translation** | **ترجمة عربية كاملة**
- **Dynamic language switching** | **تبديل ديناميكي للغة**
- **271+ translated strings** | **271+ نص مترجم**
- **RTL support for Arabic** | **دعم النصوص من اليمين لليسار للعربية**

## 📋 Request Types | أنواع الطلبات

### English | الإنجليزية
- Password Reset
- USB Access
- Extension Request
- Permission Request
- Email Request
- Technical Request
- Authorization Delegation
- Free Form

### العربية | Arabic
- إعادة تعيين كلمة المرور
- الوصول إلى USB
- طلب تمديد
- طلب صلاحية
- طلب بريد إلكتروني
- طلب تقني
- تفويض صلاحية في سقف المستخدم
- القيد الحر

## 🔄 Workflow States | حالات سير العمل

### English | الإنجليزية
1. Draft
2. Submitted
3. Direct Manager Approval
4. Audit Manager Approval
5. IT Manager Approval
6. Assigned to IT Staff
7. In Progress
8. Completed
9. Rejected

### العربية | Arabic
1. مسودة
2. مُقدم
3. موافقة المدير المباشر
4. موافقة مدير التدقيق
5. موافقة مدير تقنية المعلومات
6. مُعين لموظف تقنية المعلومات
7. قيد التنفيذ
8. مكتمل
9. مرفوض

## 👥 Security Groups | المجموعات الأمنية

### English | الإنجليزية
- Employee
- Direct Manager
- Audit Manager
- IT Manager
- IT Staff

### العربية | Arabic
- موظف
- المدير المباشر
- مدير التدقيق
- مدير تقنية المعلومات
- موظف تقنية المعلومات

## 🚀 Features | الميزات

### Core Features | الميزات الأساسية
- **Multi-stage approval workflow** | **سير عمل متعدد المراحل للموافقة**
- **Role-based permissions** | **صلاحيات قائمة على الأدوار**
- **Email notifications** | **إشعارات البريد الإلكتروني**
- **Activity logging** | **تسجيل الأنشطة**
- **Request tracking** | **تتبع الطلبات**

### Advanced Features | الميزات المتقدمة
- **Employee ID integration** | **تكامل رقم هوية الموظف**
- **Dynamic field visibility** | **رؤية ديناميكية للحقول**
- **Custom validation rules** | **قواعد تحقق مخصصة**
- **Rejection with reasons** | **الرفض مع الأسباب**
- **Completion notes** | **ملاحظات الإكمال**

## 🛠️ Installation & Configuration | التثبيت والإعداد

### 1. Install Module | تثبيت المديول
```bash
# Update module | تحديث المديول
python odoo-bin -d your_database -u bssic_requests --stop-after-init
```

### 2. Enable Arabic Language | تفعيل اللغة العربية
1. Go to Settings → Languages → Load Language → Arabic
2. اذهب إلى الإعدادات → اللغات → تحميل لغة → العربية

### 3. Set User Language | تعيين لغة المستخدم
1. Settings → Users → Select User → Preferences → Language
2. الإعدادات → المستخدمون → اختر المستخدم → التفضيلات → اللغة

### 4. Assign Security Groups | تعيين المجموعات الأمنية
Assign users to appropriate groups:
قم بتعيين المستخدمين للمجموعات المناسبة:
- BSSIC Employee | موظف BSSIC
- BSSIC Direct Manager | المدير المباشر BSSIC
- BSSIC Audit Manager | مدير التدقيق BSSIC
- BSSIC IT Manager | مدير تقنية المعلومات BSSIC
- BSSIC IT Staff | موظف تقنية المعلومات BSSIC

## 📖 Usage Guide | دليل الاستخدام

### For Employees | للموظفين
1. **Create Request** | **إنشاء طلب**
   - Navigate to BSIC Requests → My Requests
   - انتقل إلى طلبات BSIC → طلباتي
   - Click Create and fill the form
   - انقر على إنشاء واملأ النموذج

2. **Track Request** | **تتبع الطلب**
   - View request status in real-time
   - عرض حالة الطلب في الوقت الفعلي
   - Receive email notifications
   - استلام إشعارات البريد الإلكتروني

### For Managers | للمديرين
1. **Review Requests** | **مراجعة الطلبات**
   - Access All Requests menu
   - الوصول إلى قائمة جميع الطلبات
   - Filter by status and type
   - تصفية حسب الحالة والنوع

2. **Approve/Reject** | **الموافقة/الرفض**
   - Use approval buttons
   - استخدام أزرار الموافقة
   - Add rejection reasons
   - إضافة أسباب الرفض

### For IT Staff | لموظفي تقنية المعلومات
1. **Implement Requests** | **تنفيذ الطلبات**
   - View assigned requests
   - عرض الطلبات المعينة
   - Update progress status
   - تحديث حالة التقدم
   - Add completion notes
   - إضافة ملاحظات الإكمال

## 🔧 Technical Information | المعلومات التقنية

### Dependencies | التبعيات
- **base** - Core Odoo functionality
- **hr** - Human Resources module
- **mail** - Email and messaging

### Database Models | نماذج قاعدة البيانات
- `bssic.base.request` - Base request model
- `bssic.request` - Main request model
- `bssic.request.type` - Request type configuration
- `bssic.technical.category` - Technical categories

### Security Features | ميزات الأمان
- **Record Rules** | **قواعد السجلات**
- **Access Control Lists** | **قوائم التحكم في الوصول**
- **Field-level Security** | **أمان على مستوى الحقل**
- **Role-based Permissions** | **صلاحيات قائمة على الأدوار**

## 📁 File Structure | هيكل الملفات

```
bssic_requests/
├── models/                 # Python models | النماذج
├── views/                  # XML views | العروض
├── security/               # Security rules | قواعد الأمان
├── data/                   # Initial data | البيانات الأولية
├── i18n/                   # Translation files | ملفات الترجمة
│   ├── ar.po              # Arabic translations | الترجمات العربية
│   └── en.po              # English translations | الترجمات الإنجليزية
├── static/                 # Static files | الملفات الثابتة
├── tests/                  # Unit tests | اختبارات الوحدة
└── __manifest__.py         # Module manifest | بيان المديول
```

## 🧪 Testing | الاختبار

### Translation Testing | اختبار الترجمة
See `TRANSLATION_TEST.md` for comprehensive testing guide.
راجع `TRANSLATION_TEST.md` للحصول على دليل اختبار شامل.

### Unit Tests | اختبارات الوحدة
```bash
# Run tests | تشغيل الاختبارات
python odoo-bin -d test_database -i bssic_requests --test-enable --stop-after-init
```

## 📚 Documentation | التوثيق

### Additional Guides | أدلة إضافية
- `TRANSLATION_GUIDE.md` - Complete translation guide | دليل الترجمة الكامل
- `UPDATE_TRANSLATIONS.md` - Translation update summary | ملخص تحديث الترجمة
- `TRANSLATION_TEST.md` - Testing checklist | قائمة اختبار
- `REORGANIZATION_NOTES.md` - Module structure notes | ملاحظات هيكل المديول

## 🆕 Version History | تاريخ الإصدارات

### v1.0.4 (Latest) | الأحدث
- ✅ **Complete bilingual support** | **دعم ثنائي اللغة الكامل**
- ✅ **271+ translated strings** | **271+ نص مترجم**
- ✅ **Enhanced user experience** | **تجربة مستخدم محسنة**
- ✅ **RTL support for Arabic** | **دعم النصوص من اليمين لليسار**

### Previous Versions | الإصدارات السابقة
- v1.0.3 - Performance improvements
- v1.0.2 - Security enhancements
- v1.0.1 - Bug fixes and stability

## 🤝 Support | الدعم

### Getting Help | الحصول على المساعدة
- Check documentation files | راجع ملفات التوثيق
- Review translation guides | راجع أدلة الترجمة
- Test in development environment | اختبر في بيئة التطوير

### Reporting Issues | الإبلاغ عن المشاكل
- Provide detailed error messages | قدم رسائل خطأ مفصلة
- Include steps to reproduce | اشمل خطوات إعادة الإنتاج
- Specify Odoo version and environment | حدد إصدار Odoo والبيئة

---

## 🎯 Summary | الملخص

**BSIC Requests Management** is now a fully bilingual module supporting both Arabic and English with comprehensive translation coverage, enhanced user experience, and robust workflow management.

**إدارة طلبات BSIC** أصبح الآن مديولاً ثنائي اللغة بالكامل يدعم العربية والإنجليزية مع تغطية ترجمة شاملة وتجربة مستخدم محسنة وإدارة سير عمل قوية.

### Key Benefits | الفوائد الرئيسية
- 🌍 **Bilingual Interface** | **واجهة ثنائية اللغة**
- 🚀 **Enhanced Productivity** | **إنتاجية محسنة**
- 🔒 **Robust Security** | **أمان قوي**
- 📱 **User-Friendly Design** | **تصميم سهل الاستخدام**

**Ready for Production Use** | **جاهز للاستخدام في الإنتاج** ✅