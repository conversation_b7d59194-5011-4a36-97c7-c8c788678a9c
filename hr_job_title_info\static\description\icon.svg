<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- Person Icon -->
  <circle cx="64" cy="45" r="15" fill="#fff" opacity="0.9"/>
  <path d="M 35 85 Q 35 70 64 70 Q 93 70 93 85 L 93 95 L 35 95 Z" fill="#fff" opacity="0.9"/>
  
  <!-- Job Title Badge -->
  <rect x="20" y="100" width="88" height="20" rx="10" fill="#fff" opacity="0.9"/>
  <text x="64" y="113" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">JOB TITLE</text>
  
  <!-- Info Icon -->
  <circle cx="95" cy="35" r="12" fill="#27ae60"/>
  <text x="95" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#fff">i</text>
</svg>
