# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date, timedelta


class TestCareerProgression(TransactionCase):

    def setUp(self):
        super().setUp()
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'job_title': 'Junior Developer',
        })
        self.department_it = self.env['hr.department'].create({
            'name': 'IT Department',
        })
        self.department_sales = self.env['hr.department'].create({
            'name': 'Sales Department',
        })

    def test_create_career_progression(self):
        """Test creating a career progression event"""
        career_event = self.env['career.progression'].create({
            'name': 'Test Promotion',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today(),
            'previous_job_title': 'Junior Developer',
            'new_job_title': 'Senior Developer',
            'reason': 'Excellent performance',
        })

        self.assertEqual(career_event.state, 'draft')
        self.assertEqual(career_event.employee_id, self.employee)
        self.assertEqual(career_event.event_type, 'promotion')

    def test_career_progression_confirmation(self):
        """Test confirming a career progression event"""
        career_event = self.env['career.progression'].create({
            'name': 'Test Transfer',
            'employee_id': self.employee.id,
            'event_type': 'transfer',
            'event_date': date.today(),
            'previous_department_id': self.department_it.id,
            'new_department_id': self.department_sales.id,
        })

        career_event.action_confirm()
        self.assertEqual(career_event.state, 'confirmed')

    def test_temporary_assignment_validation(self):
        """Test validation for temporary assignments"""
        with self.assertRaises(ValidationError):
            self.env['career.progression'].create({
                'name': 'Test Temporary Assignment',
                'employee_id': self.employee.id,
                'event_type': 'temporary_assignment',
                'event_date': date.today(),
                'is_temporary': True,
                'temporary_start_date': date.today(),
                # Missing temporary_end_date should raise validation error
            })

    def test_temporary_assignment_dates(self):
        """Test temporary assignment date validation"""
        with self.assertRaises(ValidationError):
            self.env['career.progression'].create({
                'name': 'Test Temporary Assignment',
                'employee_id': self.employee.id,
                'event_type': 'temporary_assignment',
                'event_date': date.today(),
                'is_temporary': True,
                'temporary_start_date': date.today(),
                'temporary_end_date': date.today() - timedelta(days=1),  # End before start
            })

    def test_employee_career_progression_count(self):
        """Test employee career progression count computation"""
        initial_count = self.employee.career_progression_count

        self.env['career.progression'].create({
            'name': 'Test Event 1',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today(),
        })

        self.env['career.progression'].create({
            'name': 'Test Event 2',
            'employee_id': self.employee.id,
            'event_type': 'transfer',
            'event_date': date.today(),
        })

        self.employee._compute_career_progression_count()
        self.assertEqual(self.employee.career_progression_count, initial_count + 2)

    def test_years_in_current_position(self):
        """Test years in current position computation"""
        # Test 1: No career events - should use employee creation date
        self.employee._compute_years_in_position()
        # Should be very small (employee just created)
        self.assertLess(self.employee.years_in_current_position, 0.1)

        # Test 2: Create a career event from 2 years ago
        old_event = self.env['career.progression'].create({
            'name': 'Old Event',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today() - timedelta(days=730),  # 2 years ago
            'state': 'confirmed',
        })

        self.employee._compute_years_in_position()
        # Should be approximately 2 years (from the event date)
        self.assertGreater(self.employee.years_in_current_position, 1.9)
        self.assertLess(self.employee.years_in_current_position, 2.1)

        # Test 3: Create a more recent event (1 year ago)
        recent_event = self.env['career.progression'].create({
            'name': 'Recent Event',
            'employee_id': self.employee.id,
            'event_type': 'transfer',
            'event_date': date.today() - timedelta(days=365),  # 1 year ago
            'state': 'confirmed',
        })

        self.employee._compute_years_in_position()
        # Should be approximately 1 year (from the most recent event)
        self.assertGreater(self.employee.years_in_current_position, 0.9)
        self.assertLess(self.employee.years_in_current_position, 1.1)

    def test_auto_populate_previous_info(self):
        """Test auto-population of previous information"""
        self.employee.write({
            'job_title': 'Senior Developer',
            'department_id': self.department_it.id,
        })

        career_event = self.env['career.progression'].new({
            'employee_id': self.employee.id,
        })
        career_event._onchange_employee_id()

        self.assertEqual(career_event.previous_job_title, 'Senior Developer')
        self.assertEqual(career_event.previous_department_id, self.department_it)

    def test_event_type_onchange(self):
        """Test event type onchange behavior"""
        career_event = self.env['career.progression'].new({
            'employee_id': self.employee.id,
            'event_type': 'temporary_assignment',
        })
        career_event._onchange_event_type()

        self.assertTrue(career_event.is_temporary)

        # Change to non-temporary event type
        career_event.event_type = 'promotion'
        career_event._onchange_event_type()

        self.assertFalse(career_event.is_temporary)

    def test_job_position_update_on_confirm(self):
        """Test that employee job position is updated when career event is confirmed"""
        # Create a job position
        job_position = self.env['hr.job'].create({
            'name': 'Senior Developer',
            'department_id': self.department_it.id,
        })

        # Create career progression event
        career_event = self.env['career.progression'].create({
            'name': 'Promotion to Senior Developer',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today(),
            'new_job_id': job_position.id,
            'new_department_id': self.department_it.id,
        })

        # Confirm the event
        career_event.action_confirm()

        # Check that employee job position was updated
        self.assertEqual(self.employee.job_id, job_position)
        self.assertEqual(self.employee.department_id, self.department_it)

    def test_new_job_onchange(self):
        """Test that new department is auto-populated when job position is selected"""
        # Create a job position with department
        job_position = self.env['hr.job'].create({
            'name': 'Marketing Manager',
            'department_id': self.department_it.id,
        })

        # Create new career event
        career_event = self.env['career.progression'].new({
            'employee_id': self.employee.id,
            'new_job_id': job_position.id,
        })

        # Trigger onchange
        career_event._onchange_new_job_id()

        # Check that department was auto-populated
        self.assertEqual(career_event.new_department_id, self.department_it)

    def test_last_career_event_computation(self):
        """Test last career event computation"""
        # Test 1: No career events
        self.employee._compute_last_career_event()
        self.assertFalse(self.employee.last_career_event_date)
        self.assertFalse(self.employee.last_career_event_type)

        # Test 2: Create first career event (draft state)
        draft_event = self.env['career.progression'].create({
            'name': 'Draft Event',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today() - timedelta(days=30),
            'state': 'draft',  # Not confirmed yet
        })

        self.employee._compute_last_career_event()
        # Should still be False because event is not confirmed
        self.assertFalse(self.employee.last_career_event_date)
        self.assertFalse(self.employee.last_career_event_type)

        # Test 3: Confirm the event
        draft_event.action_confirm()
        self.employee._compute_last_career_event()
        # Now should show the event
        self.assertEqual(self.employee.last_career_event_date, draft_event.event_date)
        self.assertEqual(self.employee.last_career_event_type, 'Promotion')

        # Test 4: Create a more recent event
        recent_event = self.env['career.progression'].create({
            'name': 'Recent Event',
            'employee_id': self.employee.id,
            'event_type': 'transfer',
            'event_date': date.today() - timedelta(days=10),
            'state': 'confirmed',
        })

        self.employee._compute_last_career_event()
        # Should show the most recent event
        self.assertEqual(self.employee.last_career_event_date, recent_event.event_date)
        self.assertEqual(self.employee.last_career_event_type, 'Transfer')

    def test_readonly_after_confirm(self):
        """Test that career event becomes readonly after confirmation"""
        # Create career progression event
        career_event = self.env['career.progression'].create({
            'name': 'Test Readonly Event',
            'employee_id': self.employee.id,
            'event_type': 'promotion',
            'event_date': date.today(),
            'reason': 'Test promotion',
        })

        # Initially should be in draft state (editable)
        self.assertEqual(career_event.state, 'draft')

        # Should be able to edit in draft state
        career_event.write({'reason': 'Updated reason'})
        self.assertEqual(career_event.reason, 'Updated reason')

        # Confirm the event
        career_event.action_confirm()
        self.assertEqual(career_event.state, 'confirmed')

        # Note: The readonly behavior is enforced by the UI (attrs in views)
        # The model itself doesn't prevent writes, but the UI will show fields as readonly
        # This is the standard Odoo pattern for state-based readonly fields