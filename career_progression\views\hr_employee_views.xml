<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Employee Form View to Add Career Path Tab -->
        <record id="view_employee_form_career_progression" model="ir.ui.view">
            <field name="name">hr.employee.form.career.progression</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">

                <!-- Add Career Progression button to button box -->
                <xpath expr="//div[@name='button_box']" position="inside">
                    <button name="action_view_career_progression" type="object"
                            class="oe_stat_button" icon="fa-line-chart"
                            groups="hr.group_hr_user">
                        <field name="career_progression_count" widget="statinfo" string="Career Events"/>
                    </button>
                </xpath>

                <!-- Add the Career Path tab after HR Settings tab -->
                <xpath expr="//page[@name='hr_settings']" position="after">
                    <page name="career_path" string="Job Description" groups="hr.group_hr_user">
                        <div class="row">
                            <div class="col-12">
                                <div class="o_career_progression_header mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Career Progression Timeline</h3>
                                        <button name="action_create_career_event" type="object"
                                                string="Add Career Event"
                                                class="btn btn-primary"
                                                groups="hr.group_hr_user"/>
                                    </div>
                                </div>

                                <!-- Career Summary Cards -->
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h5 class="card-title text-primary">Years in Current Position</h5>
                                                <h2 class="text-primary">
                                                    <field name="years_in_current_position"/> years
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card border-success">
                                            <div class="card-body text-center">
                                                <h5 class="card-title text-success">Total Career Events</h5>
                                                <h2 class="text-success">
                                                    <field name="career_progression_count"/>
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card border-info">
                                            <div class="card-body text-center">
                                                <h5 class="card-title text-info">Last Career Event</h5>
                                                <p class="mb-1">
                                                    <field name="last_career_event_type"/>
                                                </p>
                                                <small class="text-muted">
                                                    <field name="last_career_event_date"/>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Assignments and Tasks Section -->
                                <div class="o_assignments_section mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="mb-0">Assignments and Tasks</h4>
                                        <button name="action_create_assignment" type="object"
                                                string="Add Assignment"
                                                class="btn btn-secondary btn-sm"
                                                groups="hr.group_hr_user"/>
                                    </div>

                                    <field name="assignment_ids" mode="tree,form"
                                           context="{'default_employee_id': active_id}">
                                        <tree string="Assignments and Tasks"
                                              decoration-info="state=='assigned'"
                                              decoration-warning="state=='in_progress'"
                                              decoration-success="state=='completed'"
                                              decoration-muted="state=='cancelled'"
                                              decoration-danger="is_overdue==True"
                                              create="true" edit="true" delete="true">
                                            <field name="start_date"/>
                                            <field name="title"/>
                                            <field name="assignment_type"/>
                                            <field name="end_date"/>
                                            <field name="priority" widget="priority"/>
                                            <field name="progress_percentage" widget="progressbar"/>
                                            <field name="state" widget="badge"
                                                   decoration-info="state=='assigned'"
                                                   decoration-warning="state=='in_progress'"
                                                   decoration-success="state=='completed'"
                                                   decoration-muted="state=='cancelled'"/>
                                            <field name="is_overdue" invisible="1"/>
                                            <button name="action_start" type="object"
                                                    string="Start"
                                                    icon="fa-play"
                                                    attrs="{'invisible': [('state', '!=', 'assigned')]}"
                                                    groups="hr.group_hr_user"/>
                                            <button name="action_complete" type="object"
                                                    string="Complete"
                                                    icon="fa-check"
                                                    attrs="{'invisible': [('state', '!=', 'in_progress')]}"
                                                    groups="hr.group_hr_user"/>
                                        </tree>
                                        <form string="Assignment">
                                            <header>
                                                <button name="action_start" string="Start" type="object"
                                                        class="oe_highlight" attrs="{'invisible': [('state', '!=', 'assigned')]}"/>
                                                <button name="action_complete" string="Complete" type="object"
                                                        class="oe_highlight" attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                                                <button name="action_cancel" string="Cancel" type="object"
                                                        attrs="{'invisible': [('state', 'in', ['completed', 'cancelled'])]}"/>
                                                <field name="state" widget="statusbar"
                                                       statusbar_visible="assigned,in_progress,completed"/>
                                            </header>
                                            <sheet>
                                                <div class="oe_title">
                                                    <h1>
                                                        <field name="title" placeholder="Assignment Title"/>
                                                    </h1>
                                                </div>

                                                <group>
                                                    <group string="Basic Information">
                                                        <field name="assignment_type"/>
                                                        <field name="priority"/>
                                                        <field name="assigned_by" options="{'no_create': True}"/>
                                                        <field name="department_id" options="{'no_create': True}"/>
                                                    </group>
                                                    <group string="Timeline">
                                                        <field name="start_date"/>
                                                        <field name="end_date"/>
                                                        <field name="duration_days" readonly="1"/>
                                                        <field name="progress_percentage" widget="progressbar"/>
                                                    </group>
                                                </group>

                                                <group string="Description">
                                                    <field name="description" nolabel="1"
                                                           placeholder="Detailed description of the assignment..."/>
                                                </group>

                                                <group string="Training Details"
                                                       attrs="{'invisible': [('assignment_type', 'not in', ['internal_training', 'external_training'])]}">
                                                    <group>
                                                        <field name="training_provider"/>
                                                        <field name="training_location"/>
                                                        <field name="training_hours"/>
                                                        <field name="certificate_received"/>
                                                    </group>
                                                </group>

                                                <group string="Completion Notes">
                                                    <field name="completion_notes" nolabel="1"
                                                           placeholder="Notes about completion or results..."/>
                                                </group>
                                            </sheet>
                                        </form>
                                    </field>
                                </div>

                                <!-- Career Progression List -->
                                <div class="o_career_progression_list">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h4 class="mb-0">Career Progression Timeline</h4>
                                    </div>

                                    <field name="career_progression_ids" mode="tree,form"
                                           context="{'default_employee_id': active_id}">
                                        <tree string="Career Events"
                                              decoration-info="state=='draft'"
                                              decoration-success="state=='confirmed'"
                                              decoration-muted="state=='cancelled'"
                                              create="false" edit="true" delete="false">
                                            <field name="event_date"/>
                                            <field name="name"/>
                                            <field name="event_type"/>
                                            <field name="previous_job_title"/>
                                            <field name="new_job_id"/>
                                            <field name="previous_department_id"/>
                                            <field name="new_department_id"/>
                                            <field name="is_temporary" invisible="1"/>
                                            <field name="temporary_end_date"
                                                   attrs="{'invisible': [('is_temporary', '=', False)]}"/>
                                            <field name="state" widget="badge"
                                                   decoration-info="state=='draft'"
                                                   decoration-success="state=='confirmed'"
                                                   decoration-muted="state=='cancelled'"/>
                                            <button name="action_confirm" type="object"
                                                    string="Confirm"
                                                    icon="fa-check"
                                                    attrs="{'invisible': [('state', '!=', 'draft')]}"
                                                    groups="hr.group_hr_manager"/>
                                        </tree>
                                        <form string="Career Event">
                                            <header>
                                                <button name="action_confirm" string="Confirm" type="object"
                                                        class="oe_highlight" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                                                <button name="action_cancel" string="Cancel" type="object"
                                                        attrs="{'invisible': [('state', 'in', ['cancelled', 'draft'])]}"/>
                                                <field name="state" widget="statusbar" statusbar_visible="draft,confirmed"/>
                                            </header>
                                            <sheet>
                                                <div class="oe_title">
                                                    <h1>
                                                        <field name="name" placeholder="Event Title"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                    </h1>
                                                </div>

                                                <group>
                                                    <group string="Event Information">
                                                        <field name="event_date"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                        <field name="event_type"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                        <field name="is_temporary" invisible="1"/>
                                                    </group>
                                                    <group string="Temporary Assignment"
                                                           attrs="{'invisible': [('event_type', '!=', 'temporary_assignment')]}">
                                                        <field name="temporary_start_date"
                                                               attrs="{'required': [('is_temporary', '=', True)], 'readonly': [('state', '=', 'confirmed')]}"/>
                                                        <field name="temporary_end_date"
                                                               attrs="{'required': [('is_temporary', '=', True)], 'readonly': [('state', '=', 'confirmed')]}"/>
                                                    </group>
                                                </group>

                                                <group>
                                                    <group string="Previous Position">
                                                        <field name="previous_job_title"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                        <field name="previous_department_id"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                    </group>
                                                    <group string="New Position">
                                                        <field name="new_job_id"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                        <field name="new_department_id"
                                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                    </group>
                                                </group>

                                                <group string="Details">
                                                    <field name="reason" placeholder="Reason for this career change..."
                                                           attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                    <field name="notes" placeholder="Additional notes..."
                                                           attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                                </group>


                                            </sheet>
                                        </form>
                                    </field>
                                </div>

                                <!-- Quick Actions -->
                                <div class="o_career_quick_actions mt-3">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="alert alert-info" role="alert">
                                                <h6 class="alert-heading">
                                                    <i class="fa fa-info-circle"/> Career Path Management
                                                </h6>
                                                <p class="mb-2">
                                                    Track and manage this employee's career progression within your organization.
                                                    Record promotions, transfers, title changes, and temporary assignments.
                                                </p>
                                                <hr/>
                                                <div class="d-flex justify-content-start">
                                                    <button name="action_view_career_progression" type="object"
                                                            string="View Full Timeline"
                                                            class="btn btn-outline-primary btn-sm me-2"/>
                                                    <button name="action_create_career_event" type="object"
                                                            string="Add New Event"
                                                            class="btn btn-outline-success btn-sm"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </page>
                </xpath>

            </field>
        </record>

        <!-- Employee Tree View Enhancement - Optional -->
        <!-- Uncomment if you want to show career progression info in employee list -->
        <!--
        <record id="view_employee_tree_career_progression" model="ir.ui.view">
            <field name="name">hr.employee.tree.career.progression</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='category_ids']" position="after">
                    <field name="years_in_current_position" optional="hide" string="Years in Position"/>
                    <field name="last_career_event_type" optional="hide" string="Last Event"/>
                    <field name="career_progression_count" optional="hide" string="Career Events"/>
                </xpath>
            </field>
        </record>
        -->

    </data>
</odoo>
