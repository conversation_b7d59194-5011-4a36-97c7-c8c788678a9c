# -*- coding: utf-8 -*-
"""
سكريبت لتفعيل التقرير التلقائي لجميع أنواع الإجازات
يمكن تشغيله من Odoo shell بعد تثبيت المديول
"""

def enable_auto_report_for_all_leave_types():
    """
    تفعيل التقرير التلقائي لجميع أنواع الإجازات الموجودة
    """
    print("=== تفعيل التقرير التلقائي لأنواع الإجازات ===")
    
    # التحقق من وجود env
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        print("استخدم: ./odoo-bin shell -d your_database")
        return
    
    try:
        # البحث عن جميع أنواع الإجازات
        leave_types = env['hr.leave.type'].search([])
        print("عدد أنواع الإجازات الموجودة: %s" % len(leave_types))
        
        updated_count = 0
        
        for leave_type in leave_types:
            # التحقق من وجود الحقل
            if hasattr(leave_type, 'auto_generate_report'):
                # تفعيل التقرير التلقائي
                leave_type.auto_generate_report = True
                updated_count += 1
                print("✓ تم تفعيل التقرير التلقائي لـ: %s" % leave_type.name)
            else:
                print("✗ حقل auto_generate_report غير موجود في: %s" % leave_type.name)
        
        # حفظ التغييرات
        if updated_count > 0:
            env.cr.commit()
            print("\n=== النتائج ===")
            print("تم تفعيل التقرير التلقائي لـ %s نوع إجازة" % updated_count)
            print("تم حفظ التغييرات في قاعدة البيانات")
        else:
            print("\nلم يتم تحديث أي نوع إجازة")
            
    except Exception as e:
        print("خطأ: %s" % str(e))
        import traceback
        traceback.print_exc()

def check_auto_report_status():
    """
    فحص حالة التقرير التلقائي لجميع أنواع الإجازات
    """
    print("=== فحص حالة التقرير التلقائي ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    try:
        leave_types = env['hr.leave.type'].search([])
        print("أنواع الإجازات وحالة التقرير التلقائي:")
        print("-" * 50)
        
        for leave_type in leave_types:
            if hasattr(leave_type, 'auto_generate_report'):
                status = "مفعل" if leave_type.auto_generate_report else "غير مفعل"
                print("%s: %s" % (leave_type.name, status))
            else:
                print("%s: حقل غير موجود" % leave_type.name)
                
    except Exception as e:
        print("خطأ: %s" % str(e))

# تشغيل الدوال
if __name__ == "__main__":
    print("استخدم الدوال التالية:")
    print("1. enable_auto_report_for_all_leave_types() - لتفعيل التقرير التلقائي")
    print("2. check_auto_report_status() - لفحص الحالة الحالية")
    print("\nمثال:")
    print("exec(open('/path/to/hr_leave_reports/enable_auto_report.py').read())")
    print("enable_auto_report_for_all_leave_types()")
