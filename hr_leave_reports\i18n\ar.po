# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_leave_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 12:00+0000\n"
"PO-Revision-Date: 2024-12-19 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"

#. module: hr_leave_reports
#: model:ir.actions.act_window,name:hr_leave_reports.hr_leave_report_wizard_action
#: model:ir.ui.menu,name:hr_leave_reports.hr_leave_advanced_report_menu
msgid "Leave Advanced Report"
msgstr "تقرير الإجازات المتقدم"

#. module: hr_leave_reports
#: model:ir.model,name:hr_leave_reports.model_hr_leave_report_wizard
msgid "تقرير الإجازات"
msgstr "تقرير الإجازات"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "تقرير الإجازات المتقدم"
msgstr "تقرير الإجازات المتقدم"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "فترة التقرير"
msgstr "فترة التقرير"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "من تاريخ"
msgstr "من تاريخ"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "إلى تاريخ"
msgstr "إلى تاريخ"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "معايير التصفية"
msgstr "معايير التصفية"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "كل الموظفين"
msgstr "كل الموظفين"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "الموظف"
msgstr "الموظف"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "حالة الإجازة"
msgstr "حالة الإجازة"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "طباعة التقرير"
msgstr "طباعة التقرير"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_wizard_form
msgid "إلغاء"
msgstr "إلغاء"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "كل الحالات"
msgstr "كل الحالات"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "مسودة"
msgstr "مسودة"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "في انتظار الموافقة"
msgstr "في انتظار الموافقة"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "موافقة أولى"
msgstr "موافقة أولى"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "موافق"
msgstr "موافق"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "مرفوض"
msgstr "مرفوض"

#. module: hr_leave_reports
#: selection:hr.leave.report.wizard,state_filter:0
msgid "ملغي"
msgstr "ملغي"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"
msgstr "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Start date must be before end date"
msgstr "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Start and end dates must be specified"
msgstr "يجب تحديد تاريخ البداية والنهاية"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "All Employees"
msgstr "كل الموظفين"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Draft"
msgstr "مسودة"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "To Approve"
msgstr "في انتظار الموافقة"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Second Approval"
msgstr "موافقة أولى"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Approved"
msgstr "موافق"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Refused"
msgstr "مرفوض"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/wizard/hr_leave_report_wizard.py:0
#, python-format
msgid "Cancelled"
msgstr "ملغي"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Leave Advanced Report"
msgstr "تقرير الإجازات المتقدم"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Period:"
msgstr "الفترة:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "From"
msgstr "من"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "To"
msgstr "إلى"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Employee:"
msgstr "الموظف:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Leave State:"
msgstr "حالة الإجازة:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Report Date:"
msgstr "تاريخ التقرير:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Total Leaves:"
msgstr "إجمالي الإجازات:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Total Days:"
msgstr "إجمالي الأيام:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "leaves"
msgstr "إجازة"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "days"
msgstr "يوم"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Employee ID"
msgstr "رقم الموظف"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Employee Name"
msgstr "اسم الموظف"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Department"
msgstr "القسم"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Leave Type"
msgstr "نوع الإجازة"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "End Date"
msgstr "تاريخ النهاية"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Days"
msgstr "الأيام"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "State"
msgstr "الحالة"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Request Date"
msgstr "تاريخ الطلب"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "No leaves found in the specified period"
msgstr "لا توجد إجازات في الفترة المحددة"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Report Summary"
msgstr "ملخص التقرير"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Average Days:"
msgstr "متوسط الأيام:"

#. module: hr_leave_reports
#: model_terms:ir.ui.view,arch_db:hr_leave_reports.hr_leave_report_template
msgid "Generated by HR Management System - Advanced Leave Reports"
msgstr "تم إنشاء هذا التقرير بواسطة نظام إدارة الموارد البشرية - تقارير الإجازات المتقدمة"

#. module: hr_leave_reports
#: model:ir.model.fields,field_description:hr_leave_reports.field_hr_leave_type__auto_generate_report
msgid "Auto Generate Report on Approval"
msgstr "إنشاء التقرير تلقائياً عند الموافقة"

#. module: hr_leave_reports
#: model:ir.model.fields,help:hr_leave_reports.field_hr_leave_type__auto_generate_report
msgid "Automatically generate and send leave report to employee when leave is approved"
msgstr "إنشاء وإرسال تقرير الإجازة للموظف تلقائياً عند الموافقة على الإجازة"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/models/hr_leave.py:0
#, python-format
msgid "Leave Report - %s - %s"
msgstr "تقرير الإجازة - %s - %s"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/models/hr_leave.py:0
#, python-format
msgid "Leave report has been automatically generated and sent to the employee."
msgstr "تم إنشاء تقرير الإجازة تلقائياً وإرساله للموظف."

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/models/hr_leave.py:0
#, python-format
msgid "Error generating leave report: %s"
msgstr "خطأ في إنشاء تقرير الإجازة: %s"

#. module: hr_leave_reports
#: code:addons/hr_leave_reports/models/hr_leave.py:0
#, python-format
msgid "Your Leave Request has been Approved - Report Attached"
msgstr "تمت الموافقة على طلب الإجازة - التقرير مرفق"
