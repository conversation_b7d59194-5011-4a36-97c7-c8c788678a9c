# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class EmployeeAssignment(models.Model):
    _name = 'employee.assignment'
    _description = 'Employee Assignments and Tasks'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'start_date desc, id desc'
    _rec_name = 'title'

    # Basic Information
    title = fields.Char(
        string='Title',
        required=True,
        help="Title of the assignment, task, or training"
    )

    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee',
        required=True,
        ondelete='cascade',
        help="Employee assigned to this task"
    )

    assignment_type = fields.Selection([
        ('task', 'Task'),
        ('assignment', 'Assignment'),
        ('internal_training', 'Internal Training'),
        ('external_training', 'External Training'),
        ('project', 'Project'),
        ('committee', 'Committee'),
        ('other', 'Other')
    ], string='Type', required=True, default='task',
       help="Type of assignment or task")

    # Dates
    start_date = fields.Date(
        string='Start Date',
        required=True,
        default=fields.Date.today,
        help="Start date of the assignment"
    )

    end_date = fields.Date(
        string='End Date',
        help="Expected or actual end date"
    )

    # Status
    state = fields.Selection([
        ('assigned', 'Assigned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('on_hold', 'On Hold')
    ], string='Status', default='assigned', required=True,
       tracking=True, help="Current status of the assignment")

    priority = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent')
    ], string='Priority', default='normal',
       help="Priority level of the assignment")

    # Details
    description = fields.Text(
        string='Description',
        help="Detailed description of the assignment or task"
    )

    objectives = fields.Text(
        string='Objectives',
        help="Objectives and expected outcomes"
    )

    # Assignment Details
    assigned_by = fields.Many2one(
        'hr.employee',
        string='Assigned By',
        help="Person who assigned this task"
    )

    department_id = fields.Many2one(
        'hr.department',
        string='Department',
        help="Department related to this assignment"
    )

    # Training Specific Fields
    training_provider = fields.Char(
        string='Training Provider',
        help="Provider or institution for training (for external training)"
    )

    training_location = fields.Char(
        string='Training Location',
        help="Location where training takes place"
    )

    training_hours = fields.Float(
        string='Training Hours',
        help="Total hours of training"
    )

    certificate_received = fields.Boolean(
        string='Certificate Received',
        help="Whether a certificate was received upon completion"
    )

    # Progress and Results
    progress_percentage = fields.Float(
        string='Progress (%)',
        default=0.0,
        tracking=True,
        help="Completion percentage (0-100)"
    )

    completion_notes = fields.Text(
        string='Completion Notes',
        help="Notes about completion or results"
    )

    # Computed Fields
    duration_days = fields.Integer(
        string='Duration (Days)',
        compute='_compute_duration',
        store=True,
        help="Duration in days"
    )

    is_overdue = fields.Boolean(
        string='Is Overdue',
        compute='_compute_overdue',
        store=True,
        help="Whether the assignment is overdue"
    )

    # Company
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.depends('start_date', 'end_date')
    def _compute_duration(self):
        for record in self:
            if record.start_date and record.end_date:
                delta = record.end_date - record.start_date
                record.duration_days = delta.days + 1
            else:
                record.duration_days = 0

    @api.depends('end_date', 'state')
    def _compute_overdue(self):
        today = fields.Date.today()
        for record in self:
            record.is_overdue = (
                record.end_date and
                record.end_date < today and
                record.state not in ['completed', 'cancelled']
            )

    @api.constrains('start_date', 'end_date')
    def _check_dates(self):
        for record in self:
            if record.start_date and record.end_date:
                if record.start_date > record.end_date:
                    raise ValidationError(_("Start date cannot be after end date."))

    @api.constrains('progress_percentage')
    def _check_progress(self):
        for record in self:
            if record.progress_percentage < 0 or record.progress_percentage > 100:
                raise ValidationError(_("Progress percentage must be between 0 and 100."))

    def action_start(self):
        """Mark assignment as in progress"""
        self.write({'state': 'in_progress'})
        return True

    def action_complete(self):
        """Mark assignment as completed"""
        self.write({
            'state': 'completed',
            'progress_percentage': 100.0
        })
        return True

    def action_cancel(self):
        """Cancel the assignment"""
        self.write({'state': 'cancelled'})
        return True

    def action_hold(self):
        """Put assignment on hold"""
        self.write({'state': 'on_hold'})
        return True

    def action_resume(self):
        """Resume assignment from hold"""
        self.write({'state': 'in_progress'})
        return True


class EmployeeAssignmentType(models.Model):
    _name = 'employee.assignment.type'
    _description = 'Assignment Types'
    _order = 'sequence, name'

    name = fields.Char(
        string='Name',
        required=True,
        help="Name of the assignment type"
    )

    code = fields.Char(
        string='Code',
        help="Short code for the assignment type"
    )

    description = fields.Text(
        string='Description',
        help="Description of this assignment type"
    )

    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Sequence for ordering"
    )

    active = fields.Boolean(
        string='Active',
        default=True,
        help="Whether this assignment type is active"
    )

    color = fields.Integer(
        string='Color',
        help="Color for display purposes"
    )
