# -*- coding: utf-8 -*-
"""
ملف اختبار بسيط للطباعة التلقائية
استخدم هذا الملف من Odoo shell لاختبار الوظيفة
"""

def test_auto_print():
    """اختبار الطباعة التلقائية"""
    print("=== اختبار الطباعة التلقائية ===")

    # التحقق من وجود env
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return

    # 1. فحص نموذج التقرير
    try:
        wizard_model = env['hr.leave.report.wizard']
        print("✓ نموذج التقرير موجود")
    except:
        print("✗ نموذج التقرير غير موجود")
        return

    # 2. فحص مرجع التقرير
    try:
        report_ref = env.ref('hr_leave_reports.hr_leave_report_pdf')
        print("✓ مرجع التقرير موجود")
    except:
        print("✗ مرجع التقرير غير موجود")
        return

    # 3. فحص أنواع الإجازات
    leave_types = env['hr.leave.type'].search([])
    print("عدد أنواع الإجازات: %s" % len(leave_types))

    for leave_type in leave_types:
        has_field = hasattr(leave_type, 'auto_generate_report')
        if has_field:
            print("  %s: %s" % (leave_type.name, leave_type.auto_generate_report))
        else:
            print("  %s: حقل غير موجود" % leave_type.name)

    # 4. اختبار إجازة موجودة
    leaves = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    if leaves:
        leave = leaves[0]
        print("اختبار إجازة: %s" % leave.employee_id.name)
        try:
            result = leave._download_leave_report_immediately(leave)
            print("✓ تم استدعاء دالة تحميل التقرير فوراً")
            print("نوع النتيجة: %s" % result.get('type', 'غير محدد'))
        except Exception as e:
            print("✗ خطأ: %s" % str(e))
    else:
        print("لا توجد إجازات موافق عليها للاختبار")

    print("=== انتهاء الاختبار ===")

def test_balance_feature():
    """اختبار ميزة الرصيد في التقرير"""
    print("=== اختبار ميزة الرصيد ===")

    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return

    # البحث عن إجازة موافق عليها
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return

    print("اختبار الرصيد للموظف: %s" % leave.employee_id.name)

    # إنشاء wizard
    wizard = env['hr.leave.report.wizard'].create({
        'date_from': leave.date_from.date(),
        'date_to': leave.date_to.date(),
        'employee_id': leave.employee_id.id,
        'all_employees': False,
        'state_filter': 'validate'
    })

    # اختبار دالة الرصيد
    balance_info = wizard.get_employee_leave_balance(
        leave.employee_id.id,
        leave.holiday_status_id.id
    )

    print("معلومات الرصيد:")
    print("  الرصيد قبل الطلب: %s أيام" % balance_info.get('balance_before', 0))
    print("  الأيام المطلوبة: %s أيام" % balance_info.get('requested_days', 0))
    print("  الرصيد بعد الطلب: %s أيام" % balance_info.get('balance_after', 0))

    wizard.unlink()
    print("✓ اختبار الرصيد مكتمل")

def test_approval_feature():
    """اختبار ميزة معلومات الموافقة في التقرير"""
    print("=== اختبار ميزة معلومات الموافقة ===")

    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return

    # البحث عن إجازة موافق عليها
    leave = env['hr.leave'].search([('state', '=', 'validate')], limit=1)
    if not leave:
        print("لا توجد إجازات موافق عليها للاختبار")
        return

    print("اختبار معلومات الموافقة للموظف: %s" % leave.employee_id.name)
    print("نوع الإجازة: %s" % leave.holiday_status_id.name)
    print("نوع التحقق: %s" % leave.validation_type)

    # إنشاء wizard
    wizard = env['hr.leave.report.wizard'].create({
        'date_from': leave.date_from.date(),
        'date_to': leave.date_to.date(),
        'employee_id': leave.employee_id.id,
        'all_employees': False,
        'state_filter': 'validate'
    })

    # اختبار دالة معلومات الموافقة
    approval_info = wizard.get_employee_approval_info(leave.employee_id.id)

    print("معلومات الموافقة:")
    print("  عرض الموافقة: %s" % approval_info.get('show_approval', False))
    print("  عدد الموافقات: %s" % approval_info.get('total_approvals', 0))

    if approval_info.get('approvals'):
        approval = approval_info['approvals'][0]  # أول موافقة
        print("  الموافق: %s" % approval.get('approved_by', 'غير محدد'))
        print("  المعتمد: %s" % approval.get('validated_by', 'غير محدد'))

    wizard.unlink()
    print("✓ اختبار معلومات الموافقة مكتمل")

# تشغيل الاختبار
if __name__ == "__main__":
    test_auto_print()
    print("\n" + "="*50 + "\n")
    test_balance_feature()
    print("\n" + "="*50 + "\n")
    test_approval_feature()
