# تطبيق الترجمة - مديول BSIC Requests
# Apply Translation - BSIC Requests Module

## 🚨 المشكلة: الترجمة لا تظهر | Problem: Translation Not Showing

إذا كانت الترجمة لا تظهر، اتبع هذه الخطوات بالترتيب:
If translation is not showing, follow these steps in order:

---

## 🔧 الحل الشامل | Complete Solution

### الخطوة 1: تفعيل اللغة العربية في النظام | Step 1: Enable Arabic in System

#### من واجهة Odoo | From Odoo Interface:
1. **تسجيل الدخول كمدير** | **Login as Administrator**
2. **اذهب إلى الإعدادات** | **Go to Settings**
3. **انقر على "اللغات"** | **Click "Languages"**
4. **انقر على "تحميل لغة"** | **Click "Load a Language"**
5. **اختر "العربية / Arabic"** | **Select "العربية / Arabic"**
6. **انقر على "تحميل"** | **Click "Load"**

### الخطوة 2: تحديث المديول | Step 2: Update Module

#### الطريقة الأولى: من واجهة Odoo | Method 1: From Odoo Interface
1. **اذهب إلى التطبيقات** | **Go to Apps**
2. **ابحث عن "bssic_requests"** | **Search for "bssic_requests"**
3. **انقر على "ترقية"** | **Click "Upgrade"**
4. **انتظر حتى اكتمال التحديث** | **Wait for update to complete**

#### الطريقة الثانية: من قاعدة البيانات | Method 2: From Database
```sql
-- تشغيل في قاعدة البيانات | Run in database
UPDATE ir_module_module 
SET state = 'to upgrade' 
WHERE name = 'bssic_requests';
```

### الخطوة 3: تحديث الترجمات | Step 3: Update Translations

#### من واجهة Odoo | From Odoo Interface:
1. **اذهب إلى الإعدادات → الترجمات** | **Go to Settings → Translations**
2. **انقر على "تحديث الترجمات"** | **Click "Update Translations"**
3. **اختر اللغة "العربية"** | **Select "Arabic" language**
4. **اختر المديول "bssic_requests"** | **Select "bssic_requests" module**
5. **انقر على "تحديث"** | **Click "Update"**

### الخطوة 4: تعيين اللغة للمستخدم | Step 4: Set User Language

#### لكل مستخدم | For Each User:
1. **انقر على اسم المستخدم** (أعلى اليمين) | **Click username** (top right)
2. **اختر "التفضيلات"** | **Select "Preferences"**
3. **في حقل "اللغة"** اختر **"العربية"** | **In "Language" field** select **"Arabic"**
4. **انقر على "حفظ"** | **Click "Save"**
5. **أعد تحميل الصفحة** | **Refresh the page**

---

## 🔍 التحقق من التطبيق | Verification

### اختبار سريع | Quick Test:
1. **اذهب إلى القائمة الرئيسية** | **Go to main menu**
2. **ابحث عن "طلبات BSIC"** | **Look for "طلبات BSIC"**
3. **انقر عليها** | **Click on it**
4. **تحقق من ظهور النصوص بالعربية** | **Verify Arabic text appears**

### إذا لم تظهر الترجمة | If Translation Doesn't Appear:

#### الحل 1: مسح ذاكرة التخزين المؤقت | Solution 1: Clear Cache
```javascript
// في متصفح الويب، اضغط F12 وشغل هذا الكود | In browser, press F12 and run this code
location.reload(true);
```

#### الحل 2: إعادة تشغيل الخادم | Solution 2: Restart Server
- أعد تشغيل خادم Odoo | Restart Odoo server
- انتظر حتى يكتمل التحميل | Wait for complete loading

#### الحل 3: التحقق من ملفات الترجمة | Solution 3: Check Translation Files
تأكد من وجود الملفات: | Ensure files exist:
- `bssic_requests/i18n/ar.po` ✅
- `bssic_requests/i18n/en.po` ✅

---

## 🛠️ حلول المشاكل الشائعة | Common Issues Solutions

### المشكلة 1: النصوص تظهر بالإنجليزية | Issue 1: Text Shows in English

**الحل:** | **Solution:**
1. تأكد من تعيين اللغة العربية للمستخدم | Ensure Arabic is set for user
2. امسح ذاكرة المتصفح | Clear browser cache
3. أعد تسجيل الدخول | Re-login

### المشكلة 2: بعض النصوص مترجمة وأخرى لا | Issue 2: Some Text Translated, Others Not

**الحل:** | **Solution:**
1. حدّث المديول مرة أخرى | Update module again
2. حدّث الترجمات من الإعدادات | Update translations from settings
3. تحقق من ملف `ar.po` | Check `ar.po` file

### المشكلة 3: الواجهة لا تدعم العربية | Issue 3: Interface Doesn't Support Arabic

**الحل:** | **Solution:**
1. تأكد من تثبيت اللغة العربية في النظام | Ensure Arabic is installed in system
2. فعّل دعم RTL في المتصفح | Enable RTL support in browser
3. استخدم متصفح حديث | Use modern browser

---

## 📋 قائمة التحقق النهائية | Final Checklist

### ✅ تأكد من: | Ensure:
- [ ] تم تثبيت اللغة العربية في النظام | Arabic language installed in system
- [ ] تم تحديث المديول | Module updated
- [ ] تم تحديث الترجمات | Translations updated
- [ ] تم تعيين اللغة العربية للمستخدم | Arabic set for user
- [ ] تم إعادة تحميل الصفحة | Page refreshed
- [ ] تم مسح ذاكرة المتصفح | Browser cache cleared

### 🎯 النتيجة المتوقعة | Expected Result:
- جميع القوائم تظهر بالعربية | All menus show in Arabic
- جميع الحقول مترجمة | All fields translated
- جميع الأزرار مترجمة | All buttons translated
- جميع الرسائل مترجمة | All messages translated

---

## 🆘 إذا استمرت المشكلة | If Problem Persists

### اتصل بالدعم الفني مع هذه المعلومات | Contact Support with This Info:

1. **إصدار Odoo** | **Odoo Version**: _______
2. **المتصفح المستخدم** | **Browser Used**: _______
3. **خطوات المحاولة** | **Steps Attempted**: _______
4. **رسائل الخطأ** | **Error Messages**: _______

### ملفات مفيدة للمراجعة | Useful Files to Review:
- `TRANSLATION_GUIDE.md` - دليل الترجمة الكامل
- `TRANSLATION_TEST.md` - اختبار الترجمة
- `QUICK_START.md` - البدء السريع
- `README.md` - معلومات شاملة

---

## 🎉 تهانينا! | Congratulations!

إذا اتبعت جميع الخطوات بنجاح، ستحصل على:
If you followed all steps successfully, you'll have:

✅ **واجهة عربية كاملة** | **Complete Arabic interface**
✅ **تبديل سلس بين اللغات** | **Smooth language switching**  
✅ **دعم كامل للنصوص من اليمين لليسار** | **Full RTL support**
✅ **271+ نص مترجم** | **271+ translated strings**

**🚀 المديول جاهز للاستخدام!** | **🚀 Module Ready to Use!**
