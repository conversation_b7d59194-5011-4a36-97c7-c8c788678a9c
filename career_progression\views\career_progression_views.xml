<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Career Progression Tree View -->
        <record id="view_career_progression_tree" model="ir.ui.view">
            <field name="name">career.progression.tree</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <tree string="Career Progression" decoration-info="state=='draft'"
                      decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'"
                      sample="1">
                    <field name="event_date"/>
                    <field name="employee_id"/>
                    <field name="name"/>
                    <field name="event_type"/>
                    <field name="previous_job_title"/>
                    <field name="new_job_id"/>
                    <field name="previous_department_id"/>
                    <field name="new_department_id"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-success="state=='confirmed'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Career Progression Form View -->
        <record id="view_career_progression_form" model="ir.ui.view">
            <field name="name">career.progression.form</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <form string="Career Progression Event">
                    <header>
                        <button name="action_confirm" string="Confirm" type="object"
                                class="oe_highlight" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_cancel" string="Cancel" type="object"
                                attrs="{'invisible': [('state', 'in', ['cancelled', 'draft'])]}"/>
                        <button name="action_reset_to_draft" string="Reset to Draft" type="object"
                                attrs="{'invisible': [('state', '!=', 'cancelled')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Event Title"
                                       attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                            </h1>
                        </div>

                        <group>
                            <group string="Basic Information">
                                <field name="employee_id" options="{'no_create': True}"
                                       attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                <field name="event_date"
                                       attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                <field name="event_type"
                                       attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                <field name="company_id" groups="base.group_multi_company"
                                       attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                            </group>
                            <group string="Temporary Assignment" attrs="{'invisible': [('event_type', '!=', 'temporary_assignment')]}">
                                <field name="is_temporary" invisible="1"/>
                                <field name="temporary_start_date"
                                       attrs="{'required': [('is_temporary', '=', True)], 'readonly': [('state', '=', 'confirmed')]}"/>
                                <field name="temporary_end_date"
                                       attrs="{'required': [('is_temporary', '=', True)], 'readonly': [('state', '=', 'confirmed')]}"/>
                            </group>
                        </group>

                        <notebook>
                            <page name="position_changes" string="Position Changes">
                                <group>
                                    <group string="Previous Position">
                                        <field name="previous_job_title"
                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                        <field name="previous_department_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                    </group>
                                    <group string="New Position">
                                        <field name="new_job_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                        <field name="new_department_id" options="{'no_create': True}"
                                               attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                    </group>
                                </group>
                            </page>

                            <page name="details" string="Details">
                                <group>
                                    <field name="reason" placeholder="Detailed reason for this career progression event..."
                                           attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                    <field name="notes" placeholder="Additional notes or comments..."
                                           attrs="{'readonly': [('state', '=', 'confirmed')]}"/>
                                </group>
                            </page>


                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Career Progression Search View -->
        <record id="view_career_progression_search" model="ir.ui.view">
            <field name="name">career.progression.search</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <search string="Career Progression">
                    <field name="name" string="Event Title"
                           filter_domain="['|', ('name', 'ilike', self), ('employee_id', 'ilike', self)]"/>
                    <field name="employee_id"/>
                    <field name="event_type"/>
                    <field name="previous_department_id"/>
                    <field name="new_department_id"/>
                    <field name="event_date"/>

                    <separator/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Confirmed" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>

                    <separator/>
                    <filter string="Promotions" name="promotions" domain="[('event_type', '=', 'promotion')]"/>
                    <filter string="Transfers" name="transfers" domain="[('event_type', '=', 'transfer')]"/>
                    <filter string="Temporary Assignments" name="temporary" domain="[('event_type', '=', 'temporary_assignment')]"/>

                    <separator/>
                    <filter string="This Year" name="this_year"
                            domain="[('event_date', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')),
                                     ('event_date', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                    <filter string="Last Year" name="last_year"
                            domain="[('event_date', '&gt;=', (datetime.datetime.now() - datetime.timedelta(days=365)).strftime('%Y-01-01')),
                                     ('event_date', '&lt;=', (datetime.datetime.now() - datetime.timedelta(days=365)).strftime('%Y-12-31'))]"/>

                    <group expand="0" string="Group By">
                        <filter string="Employee" name="group_employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                        <filter string="Event Type" name="group_event_type" domain="[]" context="{'group_by': 'event_type'}"/>
                        <filter string="Department" name="group_department" domain="[]" context="{'group_by': 'new_department_id'}"/>
                        <filter string="Event Date" name="group_date" domain="[]" context="{'group_by': 'event_date:month'}"/>
                        <filter string="Status" name="group_state" domain="[]" context="{'group_by': 'state'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Career Progression Calendar View -->
        <record id="view_career_progression_calendar" model="ir.ui.view">
            <field name="name">career.progression.calendar</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <calendar string="Career Progression Calendar"
                          date_start="event_date"
                          color="event_type"
                          quick_add="False"
                          event_open_popup="true">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="event_type"/>
                    <field name="new_job_id"/>
                    <field name="new_department_id"/>
                </calendar>
            </field>
        </record>

        <!-- Career Progression Graph View -->
        <record id="view_career_progression_graph" model="ir.ui.view">
            <field name="name">career.progression.graph</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <graph string="Career Progression Analysis" type="bar">
                    <field name="event_type" type="row"/>
                    <field name="employee_id" type="measure"/>
                </graph>
            </field>
        </record>

        <!-- Career Progression Kanban View -->
        <record id="view_career_progression_kanban" model="ir.ui.view">
            <field name="name">career.progression.kanban</field>
            <field name="model">career.progression</field>
            <field name="arch" type="xml">
                <kanban string="Career Progression" default_group_by="event_type" class="o_kanban_small_column">
                    <field name="id"/>
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="event_date"/>
                    <field name="event_type"/>
                    <field name="state"/>
                    <field name="new_job_id"/>
                    <field name="new_department_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="oe_kanban_details">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="employee_id"/>
                                        </div>
                                        <div class="o_kanban_record_body">
                                            <field name="event_date"/>
                                            <br/>
                                            <t t-if="record.new_job_id.raw_value">
                                                <strong>New Position:</strong> <field name="new_job_id"/>
                                            </t>
                                            <t t-if="record.new_department_id.raw_value">
                                                <br/><strong>Department:</strong> <field name="new_department_id"/>
                                            </t>
                                        </div>
                                        <div class="oe_kanban_bottom_right">
                                            <field name="state" widget="label_selection"
                                                   options="{'classes': {'draft': 'default', 'confirmed': 'success', 'cancelled': 'danger'}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Career Progression Action -->
        <record id="action_career_progression" model="ir.actions.act_window">
            <field name="name">Career Progression</field>
            <field name="res_model">career.progression</field>
            <field name="view_mode">tree,kanban,form,calendar,graph</field>
            <field name="search_view_id" ref="view_career_progression_search"/>
            <field name="context">{'search_default_confirmed': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new career progression event
                </p>
                <p>
                    Track employee career progression including promotions, transfers,
                    title changes, and temporary assignments. Keep a comprehensive
                    record of each employee's career journey within your organization.
                </p>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_career_progression_root"
                  name="Career Progression"
                  parent="hr.menu_hr_root"
                  sequence="50"/>

        <menuitem id="menu_career_progression"
                  name="Career Events"
                  parent="menu_career_progression_root"
                  action="action_career_progression"
                  sequence="10"/>

    </data>
</odoo>
