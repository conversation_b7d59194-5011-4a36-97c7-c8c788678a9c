# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests import common
from odoo import fields
from datetime import datetime, timedelta


class TestHrLeaveReport(common.TransactionCase):

    def setUp(self):
        super(TestHrLeaveReport, self).setUp()

        # إنشاء موظف للاختبار
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'identification_id': 'EMP001',
        })

        # إنشاء نوع إجازة للاختبار
        self.leave_type = self.env['hr.leave.type'].create({
            'name': 'Annual Leave',
            'requires_allocation': 'no',
            'auto_generate_report': True,
        })

        # إنشاء إجازة للاختبار
        self.leave = self.env['hr.leave'].create({
            'name': 'Test Leave',
            'employee_id': self.employee.id,
            'holiday_status_id': self.leave_type.id,
            'date_from': fields.Datetime.now(),
            'date_to': fields.Datetime.now() + timedelta(days=5),
            'number_of_days': 5,
        })

    def test_wizard_creation(self):
        """اختبار إنشاء wizard التقرير"""
        wizard = self.env['hr.leave.report.wizard'].create({
            'date_from': fields.Date.today(),
            'date_to': fields.Date.today() + timedelta(days=30),
            'all_employees': True,
            'state_filter': 'all',
        })

        self.assertTrue(wizard.id)
        self.assertEqual(wizard.all_employees, True)
        self.assertEqual(wizard.state_filter, 'all')

    def test_get_leave_data(self):
        """اختبار جمع بيانات الإجازات"""
        wizard = self.env['hr.leave.report.wizard'].create({
            'date_from': fields.Date.today() - timedelta(days=10),
            'date_to': fields.Date.today() + timedelta(days=10),
            'all_employees': True,
            'state_filter': 'all',
        })

        leave_data = wizard.get_leave_data()
        self.assertTrue(isinstance(leave_data, list))

    def test_date_validation(self):
        """اختبار التحقق من صحة التواريخ"""
        with self.assertRaises(Exception):
            wizard = self.env['hr.leave.report.wizard'].create({
                'date_from': fields.Date.today() + timedelta(days=10),
                'date_to': fields.Date.today(),
                'all_employees': True,
            })
            wizard._check_dates()

    def test_employee_filter(self):
        """اختبار تصفية الموظفين"""
        wizard = self.env['hr.leave.report.wizard'].create({
            'date_from': fields.Date.today() - timedelta(days=10),
            'date_to': fields.Date.today() + timedelta(days=10),
            'all_employees': False,
            'employee_id': self.employee.id,
            'state_filter': 'all',
        })

        leave_data = wizard.get_leave_data()
        # التحقق من أن البيانات تحتوي على إجازات الموظف المحدد فقط
        for leave in leave_data:
            self.assertEqual(leave['employee_code'], self.employee.identification_id or self.employee.barcode or str(self.employee.id))

    def test_report_generation(self):
        """اختبار إنشاء التقرير"""
        wizard = self.env['hr.leave.report.wizard'].create({
            'date_from': fields.Date.today() - timedelta(days=10),
            'date_to': fields.Date.today() + timedelta(days=10),
            'all_employees': True,
            'state_filter': 'all',
        })

        # اختبار إنشاء التقرير
        result = wizard.generate_report()
        self.assertTrue(result)
        self.assertEqual(result['type'], 'ir.actions.report')

    def test_auto_report_generation_on_validate(self):
        """اختبار إنشاء التقرير التلقائي عند الموافقة"""
        # التأكد من أن الإجازة في حالة مسودة
        self.assertEqual(self.leave.state, 'draft')

        # تأكيد الإجازة
        self.leave.action_confirm()

        # الموافقة على الإجازة
        self.leave.action_validate()

        # التحقق من أن الإجازة تمت الموافقة عليها
        self.assertEqual(self.leave.state, 'validate')

        # التحقق من إنشاء مرفق التقرير
        attachments = self.env['ir.attachment'].search([
            ('res_model', '=', 'hr.leave'),
            ('res_id', '=', self.leave.id),
            ('name', 'ilike', '%.pdf')
        ])
        self.assertTrue(len(attachments) > 0, "Should create PDF attachment when leave is validated")

    def test_auto_report_disabled(self):
        """اختبار عدم إنشاء التقرير عند إلغاء تفعيل الميزة"""
        # إلغاء تفعيل إنشاء التقرير التلقائي
        self.leave_type.auto_generate_report = False

        # تأكيد والموافقة على الإجازة
        self.leave.action_confirm()
        self.leave.action_validate()

        # التحقق من عدم إنشاء مرفق التقرير
        attachments = self.env['ir.attachment'].search([
            ('res_model', '=', 'hr.leave'),
            ('res_id', '=', self.leave.id),
            ('name', 'ilike', '%.pdf')
        ])
        self.assertEqual(len(attachments), 0, "Should not create PDF attachment when auto report is disabled")
