# Career Progression Module for Odoo 15

## نظرة عامة

موديول "Career Progression" هو حل شامل لإدارة وتتبع المسار الوظيفي للموظفين في Odoo 15. يوفر هذا الموديول واجهة احترافية لتسجيل ومتابعة جميع التطورات الوظيفية للموظفين بما في ذلك الترقيات والنقل والتعديلات في المسميات الوظيفية.

## الميزات الرئيسية

### 🎯 تبويب "Career Path" في نموذج الموظف
- تبويب جديد يظهر بجانب "HR Settings" في نموذج الموظف
- عرض timeline احترافي لمسار الموظف الوظيفي
- بطاقات إحصائية تظهر ملخص المسار الوظيفي

### 📊 أنواع الأحداث الوظيفية
- **النقل (Transfer)**: نقل الموظف بين الإدارات أو الفروع
- **الترقية (Promotion)**: ترقية الموظف لمنصب أعلى
- **تعديل المسمى (Title Change)**: تغيير المسمى الوظيفي
- **المهام المؤقتة (Temporary Assignment)**: تكليفات مؤقتة
- **تغيير الإدارة (Department Change)**: انتقال بين الإدارات
- **تغيير الموقع (Location Change)**: تغيير مكان العمل

### 📋 الحقول والمعلومات المتتبعة
- تاريخ الحدث الوظيفي
- نوع الحدث
- المسمى الوظيفي السابق والجديد
- الإدارة/الفرع السابق والجديد
- موقع العمل السابق والجديد
- سبب التغيير (نص مفصل)
- ملاحظات إضافية
- دعم المرفقات (PDF، صور، مستندات)

### 🔄 إدارة المهام المؤقتة
- تحديد تواريخ البداية والنهاية للمهام المؤقتة
- تتبع المهام الاستثنائية والمؤقتة
- إدارة التكليفات قصيرة المدى

### 📈 واجهات متعددة للعرض
- **Tree View**: عرض قائمة بجميع الأحداث الوظيفية
- **Form View**: نموذج مفصل لإدخال وتعديل الأحداث
- **Calendar View**: عرض تقويمي للأحداث الوظيفية
- **Graph View**: تحليل إحصائي للمسار الوظيفي
- **Kanban View**: عرض بطاقات منظم حسب نوع الحدث

### 🔍 البحث والتصفية المتقدمة
- البحث بالموظف أو عنوان الحدث
- تصفية حسب نوع الحدث
- تصفية حسب الحالة (مسودة، مؤكد، ملغي)
- تصفية زمنية (هذا العام، العام الماضي)
- تجميع حسب الموظف، نوع الحدث، الإدارة، التاريخ

### 🔐 إدارة الصلاحيات
- **HR User**: قراءة وكتابة وإنشاء الأحداث
- **HR Manager**: صلاحيات كاملة بما في ذلك الحذف
- **Employee**: قراءة فقط للأحداث الخاصة بهم

## التثبيت والإعداد

### متطلبات النظام
- Odoo 15.0+
- موديول `hr` (الموارد البشرية الأساسي)
- موديول `mail` (للرسائل والأنشطة)

### خطوات التثبيت
1. انسخ مجلد `career_progression` إلى مجلد addons في Odoo
2. قم بتحديث قائمة التطبيقات
3. ابحث عن "Career Progression" وقم بتثبيته
4. سيتم إنشاء البيانات الأساسية تلقائياً

### الإعداد الأولي
بعد التثبيت، ستجد:
- تبويب "Career Path" جديد في نموذج الموظف
- قائمة "Career Progression" في قائمة الموارد البشرية
- أنواع الأحداث الوظيفية محددة مسبقاً

## كيفية الاستخدام

### إضافة حدث وظيفي جديد
1. افتح ملف الموظف المطلوب
2. انتقل إلى تبويب "Career Path"
3. اضغط على "Add Career Event"
4. املأ تفاصيل الحدث الوظيفي
5. أرفق المستندات الداعمة إذا لزم الأمر
6. احفظ وأكد الحدث

### عرض المسار الوظيفي
- من تبويب "Career Path" في ملف الموظف
- من قائمة "Career Progression" في الموارد البشرية
- استخدم Timeline View لعرض تسلسل زمني تفاعلي

### إدارة المهام المؤقتة
1. اختر نوع الحدث "Temporary Assignment"
2. حدد تاريخ البداية والنهاية
3. املأ تفاصيل المهمة المؤقتة
4. أكد الحدث

## البنية التقنية

### النماذج (Models)
- `career.progression`: النموذج الرئيسي للأحداث الوظيفية
- `career.progression.event.type`: أنواع الأحداث الوظيفية
- `hr.employee`: توسيع نموذج الموظف

### الحقول المحسوبة
- `career_progression_count`: عدد الأحداث الوظيفية
- `last_career_event_date`: تاريخ آخر حدث وظيفي
- `last_career_event_type`: نوع آخر حدث وظيفي
- `years_in_current_position`: عدد السنوات في المنصب الحالي

### الواجهات (Views)
- واجهات شجرة ونموذج ومخطط زمني وكانبان
- واجهة بحث متقدمة مع تصفية وتجميع
- تكامل مع نموذج الموظف

## الدعم والصيانة

### التحديثات المستقبلية
- إضافة تقارير تحليلية للمسارات الوظيفية
- تكامل مع نظام التقييم السنوي
- إشعارات تلقائية للمهام المؤقتة المنتهية الصلاحية

### المساهمة
نرحب بالمساهمات لتطوير هذا الموديول. يرجى اتباع معايير Odoo في التطوير.

## الترخيص
هذا الموديول مرخص تحت LGPL-3

---

**تم تطوير هذا الموديول وفقاً لأفضل الممارسات في Odoo 15 مع التركيز على الأداء والأمان وسهولة الاستخدام.**
