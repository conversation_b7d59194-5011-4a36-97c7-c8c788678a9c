# إصلاح مشكلة عدم ظهور تفاصيل طلبات الصلاحيات
# Fix for Permission Request Fields Not Showing Issue

## المشكلة / Problem
عند فتح طلبات الصلاحيات في "My Requests" أو "All Requests"، لا تظهر تفاصيل الطلب في تبويب "Request Details".

When opening permission requests in "My Requests" or "All Requests", the request details don't show in the "Request Details" tab.

## السبب / Root Cause
المشكلة تكمن في أن الحقل `show_permission_fields` في نوع الطلب "Permission Request" قد لا يكون مُفعلاً بشكل صحيح، مما يجعل جميع حقول تفاصيل الصلاحيات مخفية.

The issue is that the `show_permission_fields` field in the "Permission Request" type may not be properly enabled, causing all permission detail fields to be hidden.

## الحلول المتاحة / Available Solutions

### الحل الأول: تحديث المديول / Solution 1: Update Module
```bash
# تحديث المديول لتطبيق الإصلاحات الجديدة
# Update the module to apply new fixes
python odoo-bin -u bssic_requests -d your_database_name
```

### الحل الثاني: تشغيل سكريبت Python / Solution 2: Run Python Script
```bash
# تشغيل السكريبت من Odoo shell
# Run script from Odoo shell
python odoo-bin shell -d your_database_name
```

ثم في shell:
```python
exec(open('fix_permission_fields_simple.py').read())
```

### الحل الثالث: تشغيل SQL مباشرة / Solution 3: Run SQL Directly
```sql
-- تحديث نوع طلب الصلاحيات
-- Update permission request type
UPDATE bssic_request_type 
SET 
    show_permission_fields = true,
    active = true
WHERE code = 'permission';

-- التحقق من النتيجة
-- Verify result
SELECT name, code, show_permission_fields, active 
FROM bssic_request_type 
WHERE code = 'permission';
```

### الحل الرابع: من واجهة Odoo / Solution 4: From Odoo Interface
1. اذهب إلى Settings > Technical > Database Structure > Models
2. ابحث عن `bssic.request.type`
3. افتح السجل الذي code = 'permission'
4. تأكد من أن `show_permission_fields` = True
5. تأكد من أن `active` = True

## التحقق من الإصلاح / Verification

### 1. فحص نوع الطلب / Check Request Type
```sql
SELECT 
    id, name, code, active, show_permission_fields
FROM bssic_request_type 
WHERE code = 'permission';
```

يجب أن تكون النتيجة:
- `show_permission_fields` = `true`
- `active` = `true`

### 2. فحص طلب موجود / Check Existing Request
1. افتح طلب صلاحيات موجود
2. تحقق من ظهور الحقول في تبويب "Request Details"
3. يجب أن تظهر:
   - Permission Request Details
   - Department Permissions
   - Transaction Limits

### 3. إنشاء طلب جديد / Create New Request
1. اذهب إلى BSIC Requests > Requests > Permission Request
2. أنشئ طلب جديد
3. تحقق من ظهور جميع الحقول المطلوبة

## الملفات المُحدثة / Updated Files

1. `bssic_requests/data/request_type_data.xml` - إضافة `active` field
2. `bssic_requests/data/update_request_types.xml` - ملف جديد لتحديث الإعدادات
3. `bssic_requests/__manifest__.py` - إضافة ملف التحديث
4. `fix_permission_fields_simple.py` - سكريبت الإصلاح
5. `check_permission_data.sql` - سكريبت فحص SQL

## ملاحظات مهمة / Important Notes

1. **إعادة تحميل الصفحة**: بعد تطبيق الإصلاح، قم بإعادة تحميل صفحة Odoo
2. **مسح الكاش**: قد تحتاج لمسح كاش المتصفح
3. **إعادة تسجيل الدخول**: في بعض الحالات قد تحتاج لإعادة تسجيل الدخول
4. **Developer Mode**: تأكد من تفعيل Developer Mode للوصول للأدوات التقنية

## استكشاف الأخطاء / Troubleshooting

### إذا لم تظهر الحقول بعد الإصلاح / If Fields Still Don't Show
1. تحقق من أن `show_permission_fields` = True في قاعدة البيانات
2. تحقق من أن العرض يحتوي على الحقل `show_permission_fields`
3. تحقق من أن الطلب مرتبط بنوع الطلب الصحيح
4. جرب إنشاء طلب جديد بدلاً من فتح طلب قديم

### رسائل الخطأ الشائعة / Common Error Messages
- "Field not found": تأكد من تحديث المديول
- "Access denied": تأكد من الصلاحيات المناسبة
- "View not found": تأكد من وجود ملفات العرض

## الدعم / Support
إذا استمرت المشكلة، يرجى:
1. التحقق من سجلات Odoo (logs)
2. تشغيل Odoo في وضع التطوير (debug mode)
3. فحص قاعدة البيانات مباشرة

If the issue persists, please:
1. Check Odoo logs
2. Run Odoo in debug mode  
3. Examine the database directly
