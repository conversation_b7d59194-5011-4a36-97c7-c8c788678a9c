# دليل البدء السريع - مديول BSIC Requests
# Quick Start Guide - BSIC Requests Module

## 🚀 البدء السريع | Quick Start

### خطوة 1: تفعيل اللغة العربية | Step 1: Enable Arabic Language

#### العربية | Arabic
1. اذهب إلى **الإعدادات** → **اللغات**
2. انقر على **"تحميل لغة"**
3. اختر **"العربية"** من القائمة
4. انقر على **"تحميل"**

#### English
1. Go to **Settings** → **Languages**
2. Click **"Load a Language"**
3. Select **"Arabic"** from the list
4. Click **"Load"**

### خطوة 2: تغيير لغة المستخدم | Step 2: Change User Language

#### العربية | Arabic
1. انقر على **اسم المستخدم** في الزاوية العلوية اليمنى
2. اختر **"التفضيلات"**
3. في حقل **"اللغة"** اختر **"العربية"**
4. انقر على **"حفظ"**

#### English
1. Click on **username** in the top right corner
2. Select **"Preferences"**
3. In **"Language"** field select **"Arabic"**
4. Click **"Save"**

### خطوة 3: الوصول إلى المديول | Step 3: Access Module

#### العربية | Arabic
1. في القائمة الرئيسية، ابحث عن **"طلبات BSIC"**
2. انقر عليها للوصول إلى المديول

#### English
1. In the main menu, look for **"BSIC Requests"**
2. Click on it to access the module

---

## 📋 إنشاء طلب جديد | Creating a New Request

### للموظفين | For Employees

#### العربية | Arabic
1. اذهب إلى **طلبات BSIC** → **طلباتي**
2. انقر على **"إنشاء"**
3. اختر **نوع الطلب** من القائمة المنسدلة
4. املأ **المعلومات المطلوبة**
5. انقر على **"حفظ"**
6. انقر على **"إرسال"** لتقديم الطلب

#### English
1. Go to **BSIC Requests** → **My Requests**
2. Click **"Create"**
3. Select **Request Type** from dropdown
4. Fill in **required information**
5. Click **"Save"**
6. Click **"Submit"** to submit the request

---

## 🔄 أنواع الطلبات المتاحة | Available Request Types

### 1. إعادة تعيين كلمة المرور | Password Reset
- **الحقول المطلوبة | Required Fields:**
  - اسم المستخدم | Username
  - نوع الجهاز | Device Type
  - سبب الطلب | Request Reason

### 2. الوصول إلى USB | USB Access
- **الحقول المطلوبة | Required Fields:**
  - الغرض من الاستخدام | Purpose of Usage
  - المدة المطلوبة | Required Duration
  - نوع البيانات | Data Type

### 3. طلب تمديد | Extension Request
- **الحقول المطلوبة | Required Fields:**
  - فترة التمديد | Extension Period
  - سبب التمديد | Extension Reason

### 4. طلب صلاحية | Permission Request
- **الحقول المطلوبة | Required Fields:**
  - نوع الصلاحية | Permission Type
  - اسم المستخدم | User Name
  - صالح من/إلى | Valid From/To

### 5. طلب بريد إلكتروني | Email Request
- **الحقول المطلوبة | Required Fields:**
  - نوع البريد | Email Type
  - البريد المطلوب | Requested Email
  - الغرض | Purpose

### 6. طلب تقني | Technical Request
- **الحقول المطلوبة | Required Fields:**
  - الفئة | Category
  - الفئة الفرعية | Subcategory
  - الأولوية | Priority

### 7. تفويض صلاحية | Authorization Delegation
- **الحقول المطلوبة | Required Fields:**
  - سبب رفع السقف | Ceiling Reason
  - التفاصيل | Details
  - التاريخ من/إلى | From/To Date
  - الحد الأقصى | Max Amount

### 8. القيد الحر | Free Form
- **الحقول المطلوبة | Required Fields:**
  - الموضوع | Subject
  - تفاصيل العملية | Operation Details
  - نوع القيد | Entry Type

---

## 👥 الأدوار والصلاحيات | Roles and Permissions

### موظف | Employee
- ✅ إنشاء طلبات جديدة | Create new requests
- ✅ عرض طلباته الخاصة | View own requests
- ✅ تتبع حالة الطلبات | Track request status

### المدير المباشر | Direct Manager
- ✅ عرض طلبات فريقه | View team requests
- ✅ الموافقة على الطلبات | Approve requests
- ✅ رفض الطلبات مع السبب | Reject with reason

### مدير التدقيق | Audit Manager
- ✅ عرض جميع الطلبات | View all requests
- ✅ الموافقة بعد المدير المباشر | Approve after direct manager
- ✅ مراجعة سجل الأنشطة | Review activity log

### مدير تقنية المعلومات | IT Manager
- ✅ إدارة جميع الطلبات | Manage all requests
- ✅ تعيين الطلبات للموظفين | Assign to staff
- ✅ الموافقة النهائية | Final approval

### موظف تقنية المعلومات | IT Staff
- ✅ عرض الطلبات المعينة | View assigned requests
- ✅ تحديث حالة التنفيذ | Update progress
- ✅ إضافة ملاحظات الإكمال | Add completion notes

---

## 🔍 تتبع الطلبات | Request Tracking

### حالات الطلب | Request States

#### العربية | Arabic
1. **مسودة** - الطلب قيد الإنشاء
2. **مُقدم** - تم تقديم الطلب
3. **موافقة المدير المباشر** - في انتظار موافقة المدير
4. **موافقة مدير التدقيق** - في انتظار موافقة التدقيق
5. **موافقة مدير تقنية المعلومات** - في انتظار موافقة تقنية المعلومات
6. **مُعين لموظف تقنية المعلومات** - تم تعيين الطلب
7. **قيد التنفيذ** - جاري تنفيذ الطلب
8. **مكتمل** - تم إكمال الطلب
9. **مرفوض** - تم رفض الطلب

#### English
1. **Draft** - Request being created
2. **Submitted** - Request submitted
3. **Direct Manager Approval** - Awaiting manager approval
4. **Audit Manager Approval** - Awaiting audit approval
5. **IT Manager Approval** - Awaiting IT approval
6. **Assigned to IT Staff** - Request assigned
7. **In Progress** - Request being implemented
8. **Completed** - Request completed
9. **Rejected** - Request rejected

---

## 📧 الإشعارات | Notifications

### متى تستلم إشعارات | When You Receive Notifications
- ✅ عند تقديم طلب جديد | New request submission
- ✅ عند الموافقة على الطلب | Request approval
- ✅ عند رفض الطلب | Request rejection
- ✅ عند تعيين الطلب | Request assignment
- ✅ عند إكمال الطلب | Request completion

---

## ❓ الأسئلة الشائعة | FAQ

### س: كيف أغير لغة الواجهة؟ | Q: How to change interface language?
**ج:** اذهب إلى التفضيلات واختر اللغة المطلوبة
**A:** Go to Preferences and select desired language

### س: لماذا لا أستطيع رؤية بعض الطلبات؟ | Q: Why can't I see some requests?
**ج:** تحقق من صلاحياتك ومجموعة المستخدم الخاصة بك
**A:** Check your permissions and user group

### س: كيف أتتبع حالة طلبي؟ | Q: How to track my request status?
**ج:** اذهب إلى "طلباتي" وستجد جميع طلباتك مع حالاتها
**A:** Go to "My Requests" to see all your requests with their status

### س: ماذا أفعل إذا رُفض طلبي؟ | Q: What if my request is rejected?
**ج:** ستجد سبب الرفض في تفاصيل الطلب، يمكنك إنشاء طلب جديد بعد معالجة السبب
**A:** You'll find rejection reason in request details, you can create new request after addressing the issue

---

## 🆘 الدعم | Support

### في حالة وجود مشاكل | In Case of Issues
1. **تحقق من الصلاحيات** | **Check permissions**
2. **أعد تحميل الصفحة** | **Refresh page**
3. **تواصل مع مدير النظام** | **Contact system admin**

### ملفات المساعدة | Help Files
- `README.md` - دليل شامل | Complete guide
- `TRANSLATION_GUIDE.md` - دليل الترجمة | Translation guide
- `TRANSLATION_TEST.md` - اختبار الترجمة | Translation testing

---

**🎉 مبروك! أنت الآن جاهز لاستخدام مديول BSIC Requests**
**🎉 Congratulations! You're now ready to use BSIC Requests module**
