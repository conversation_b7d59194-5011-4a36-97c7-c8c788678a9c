<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>HR Job Title Info</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #3498db;
            border-radius: 5px;
        }
        .feature-list li strong {
            color: #2c3e50;
        }
        .highlight {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #27ae60;
            margin: 20px 0;
        }
        .arabic {
            direction: rtl;
            text-align: right;
            font-family: 'Arial', sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HR Job Title Info</h1>
        
        <div class="highlight">
            <h2>📋 نظرة عامة</h2>
            <p class="arabic">
                موديول مخصص لإضافة تبويبة "معلومات المسمى الوظيفي" إلى نموذج الموظف في Odoo 15.
                يوفر معلومات تفصيلية عن المنصب الوظيفي الحالي للموظف.
            </p>
        </div>

        <h2>✨ الميزات الرئيسية</h2>
        <ul class="feature-list">
            <li><strong>المسمى الوظيفي:</strong> حقل قابل للتحرير لتحديد المسمى الوظيفي الدقيق</li>
            <li><strong>تاريخ البدء:</strong> تاريخ بدء الموظف في هذا المنصب</li>
            <li><strong>القسم المرتبط:</strong> ربط بالقسم المسؤول عن هذا المنصب</li>
            <li><strong>المسؤوليات:</strong> وصف تفصيلي لمهام ومسؤوليات الوظيفة</li>
            <li><strong>المرتبة الوظيفية:</strong> تحديد المستوى المهني (مبتدئ، متوسط، أول، إلخ)</li>
            <li><strong>ملاحظات إضافية:</strong> مساحة للملاحظات والتعليقات الإضافية</li>
        </ul>

        <h2>🎯 المستويات المهنية</h2>
        <ul class="feature-list">
            <li>مستوى مبتدئ (Entry Level)</li>
            <li>مبتدئ (Junior)</li>
            <li>مستوى متوسط (Mid Level)</li>
            <li>أول (Senior)</li>
            <li>قائد فريق (Lead)</li>
            <li>مدير (Manager)</li>
            <li>مدير عام (Director)</li>
            <li>تنفيذي (Executive)</li>
        </ul>

        <h2>🔧 الميزات التقنية</h2>
        <ul class="feature-list">
            <li><strong>التوافق:</strong> Odoo 15</li>
            <li><strong>الترجمة:</strong> دعم كامل للغة العربية</li>
            <li><strong>الأمان:</strong> صلاحيات مناسبة لجميع مجموعات المستخدمين</li>
            <li><strong>التتبع:</strong> تتبع جميع التغييرات على الحقول</li>
            <li><strong>التحديث التلقائي:</strong> ربط ذكي بين الحقول المترابطة</li>
        </ul>

        <div class="highlight">
            <h2>🚀 سهولة الاستخدام</h2>
            <p>
                التبويبة الجديدة تظهر بجانب تبويبة "HR Settings" وتوفر واجهة سهلة ومنظمة 
                لإدارة معلومات المسمى الوظيفي بشكل احترافي ومتوافق مع تصميم Odoo الرسمي.
            </p>
        </div>
    </div>
</body>
</html>
