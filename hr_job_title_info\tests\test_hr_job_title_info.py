# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo import fields


class TestHrJobTitleInfo(TransactionCase):

    def setUp(self):
        super(TestHrJobTitleInfo, self).setUp()
        
        # Create test department
        self.department = self.env['hr.department'].create({
            'name': 'Test Department',
        })
        
        # Create test job
        self.job = self.env['hr.job'].create({
            'name': 'Test Job Position',
            'department_id': self.department.id,
        })
        
        # Create test employee
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'job_id': self.job.id,
            'department_id': self.department.id,
        })

    def test_job_title_info_fields_exist(self):
        """Test that all job title info fields exist on hr.employee model"""
        
        # Check that all new fields exist
        self.assertTrue(hasattr(self.employee, 'job_title_custom'))
        self.assertTrue(hasattr(self.employee, 'job_start_date'))
        self.assertTrue(hasattr(self.employee, 'job_related_department'))
        self.assertTrue(hasattr(self.employee, 'job_responsibilities'))
        self.assertTrue(hasattr(self.employee, 'job_level'))
        self.assertTrue(hasattr(self.employee, 'job_additional_notes'))

    def test_job_title_info_values(self):
        """Test setting and getting job title info values"""
        
        # Set values
        self.employee.write({
            'job_title_custom': 'Senior Developer',
            'job_start_date': fields.Date.today(),
            'job_related_department': self.department.id,
            'job_responsibilities': 'Develop and maintain software applications',
            'job_level': 'senior',
            'job_additional_notes': 'Excellent performance',
        })
        
        # Check values
        self.assertEqual(self.employee.job_title_custom, 'Senior Developer')
        self.assertEqual(self.employee.job_start_date, fields.Date.today())
        self.assertEqual(self.employee.job_related_department.id, self.department.id)
        self.assertEqual(self.employee.job_responsibilities, 'Develop and maintain software applications')
        self.assertEqual(self.employee.job_level, 'senior')
        self.assertEqual(self.employee.job_additional_notes, 'Excellent performance')

    def test_onchange_job_id(self):
        """Test auto-population when job_id changes"""
        
        # Change job_id and trigger onchange
        self.employee.job_id = self.job.id
        self.employee._onchange_job_id()
        
        # Check that job_title_custom is auto-populated
        self.assertEqual(self.employee.job_title_custom, self.job.name)
        self.assertEqual(self.employee.job_related_department.id, self.department.id)

    def test_onchange_department_id(self):
        """Test auto-population when department_id changes"""
        
        # Clear job_related_department first
        self.employee.job_related_department = False
        
        # Change department_id and trigger onchange
        self.employee.department_id = self.department.id
        self.employee._onchange_department_id()
        
        # Check that job_related_department is auto-populated
        self.assertEqual(self.employee.job_related_department.id, self.department.id)

    def test_job_level_selection(self):
        """Test job level selection values"""
        
        valid_levels = ['entry', 'junior', 'mid', 'senior', 'lead', 'manager', 'director', 'executive']
        
        for level in valid_levels:
            self.employee.job_level = level
            self.assertEqual(self.employee.job_level, level)
