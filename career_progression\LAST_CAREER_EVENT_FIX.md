# إصلاح حقل Last Career Event

## 🐛 **المشكلة:**

حقل "Last Career Event" لا يعمل بشكل صحيح إلا بعد إضافة حدث وظيفي ثاني.

## 🔍 **سبب المشكلة:**

### Dependencies ناقصة:
```python
# قبل الإصلاح
@api.depends('career_progression_ids.event_date', 'career_progression_ids.event_type')
def _compute_last_career_event(self):
    # المشكلة: لا يتتبع تغييرات state
```

### المشكلة:
- عندما يتم تأكيد الحدث الأول (`state` يتغير من `draft` إلى `confirmed`)
- الحقل لا يتحدث لأن `state` ليس في dependencies
- يحتاج حدث ثاني لإجبار إعادة الحساب

## ✅ **الحل المطبق:**

### 1. **إضافة state إلى dependencies:**
```python
@api.depends('career_progression_ids.event_date', 'career_progression_ids.event_type', 'career_progression_ids.state')
def _compute_last_career_event(self):
    for employee in self:
        last_event = employee.career_progression_ids.filtered(
            lambda x: x.state == 'confirmed'  # فقط الأحداث المؤكدة
        ).sorted('event_date', reverse=True)[:1]

        if last_event:
            employee.last_career_event_date = last_event.event_date
            employee.last_career_event_type = dict(
                last_event._fields['event_type'].selection
            )[last_event.event_type]
        else:
            employee.last_career_event_date = False
            employee.last_career_event_type = False
```

### 2. **إضافة store=True لـ years_in_current_position:**
```python
years_in_current_position = fields.Float(
    string='Years in Current Position',
    compute='_compute_years_in_position',
    store=True,  # ← مضاف
    help="Number of years in current position"
)
```

## 🔄 **كيف يعمل الآن:**

### السيناريو 1: إنشاء حدث جديد (Draft)
```
Event created: state = 'draft'
↓
Last Career Event: لا يظهر (صحيح)
Years in Position: لا يتأثر (صحيح)
```

### السيناريو 2: تأكيد الحدث الأول
```
User clicks "Confirm"
↓
state changes: 'draft' → 'confirmed'
↓
Dependencies trigger: career_progression_ids.state
↓
_compute_last_career_event() runs
↓
Last Career Event: يظهر فوراً ✅
Years in Position: يتحدث فوراً ✅
```

### السيناريو 3: إضافة حدث ثاني
```
New event confirmed
↓
Dependencies trigger: event_date, state
↓
Last Career Event: يتحدث لآخر حدث ✅
Years in Position: يحسب من آخر حدث ✅
```

## 🧪 **الاختبارات المضافة:**

### اختبار شامل للسيناريوهات:
```python
def test_last_career_event_computation(self):
    # Test 1: No events
    self.assertFalse(self.employee.last_career_event_date)
    
    # Test 2: Draft event (should not show)
    draft_event = self.env['career.progression'].create({
        'state': 'draft',
        ...
    })
    self.assertFalse(self.employee.last_career_event_date)
    
    # Test 3: Confirm event (should show immediately)
    draft_event.action_confirm()
    self.assertEqual(self.employee.last_career_event_date, draft_event.event_date)
    
    # Test 4: More recent event (should update)
    recent_event = self.env['career.progression'].create({
        'state': 'confirmed',
        ...
    })
    self.assertEqual(self.employee.last_career_event_date, recent_event.event_date)
```

## 📋 **أمثلة عملية:**

### المثال الأول: موظف جديد
```
Employee: John Doe
Career Events: None

Last Career Event Date: False
Last Career Event Type: False
Years in Position: 0.0 (من تاريخ الإنشاء)
```

### المثال الثاني: حدث واحد مؤكد
```
Employee: John Doe
Event 1: 2023-06-01 - Promotion (Confirmed)

Last Career Event Date: 2023-06-01 ✅
Last Career Event Type: "Promotion" ✅
Years in Position: 0.6 years ✅
```

### المثال الثالث: أحداث متعددة
```
Employee: John Doe
Event 1: 2023-01-01 - Promotion (Confirmed)
Event 2: 2023-06-01 - Transfer (Confirmed)
Event 3: 2023-12-01 - Promotion (Draft) ← لا يظهر

Last Career Event Date: 2023-06-01 ✅ (آخر حدث مؤكد)
Last Career Event Type: "Transfer" ✅
Years in Position: 0.5 years ✅ (من 2023-06-01)
```

## 🎯 **المزايا:**

### 1. **تحديث فوري:**
- ✅ يعمل من الحدث الأول
- ✅ يتحدث فور التأكيد
- ✅ لا يحتاج حدث ثاني

### 2. **دقة في البيانات:**
- ✅ يعتبر فقط الأحداث المؤكدة
- ✅ يتجاهل الأحداث المسودة
- ✅ يأخذ آخر حدث بالترتيب الزمني

### 3. **أداء محسن:**
- ✅ `store=True` يحفظ القيم في قاعدة البيانات
- ✅ Dependencies دقيقة تمنع الحسابات غير الضرورية
- ✅ إعادة حساب تلقائية عند التغيير

## 🔧 **للمطورين:**

### تشغيل الاختبارات:
```bash
# اختبار Last Career Event
python -m pytest career_progression/tests/test_career_progression.py::TestCareerProgression::test_last_career_event_computation

# اختبار Years in Position
python -m pytest career_progression/tests/test_career_progression.py::TestCareerProgression::test_years_in_current_position
```

### إعادة حساب يدوي:
```python
# من Odoo shell
employee = self.env['hr.employee'].browse(employee_id)
employee._compute_last_career_event()
employee._compute_years_in_position()
print(f"Last event: {employee.last_career_event_date}")
print(f"Years in position: {employee.years_in_current_position}")
```

### فرض إعادة حساب لجميع الموظفين:
```python
# إذا كانت هناك بيانات قديمة تحتاج تحديث
employees = self.env['hr.employee'].search([])
employees._compute_last_career_event()
employees._compute_years_in_position()
```

## ⚠️ **ملاحظات مهمة:**

### 1. **الأحداث المؤكدة فقط:**
- يعتبر فقط `state == 'confirmed'`
- الأحداث في حالة `draft` أو `cancelled` لا تظهر

### 2. **الترتيب الزمني:**
- يأخذ آخر حدث حسب `event_date`
- إذا كان هناك أحداث في نفس التاريخ، يأخذ آخر واحد

### 3. **Dependencies:**
- تغيير `event_date` يؤدي لإعادة الحساب
- تغيير `event_type` يؤدي لإعادة الحساب  
- تغيير `state` يؤدي لإعادة الحساب

## 🎉 **النتيجة:**

الآن حقول "Last Career Event" و "Years in Current Position" تعمل بشكل صحيح من الحدث الأول، مع تحديث فوري عند التأكيد!
