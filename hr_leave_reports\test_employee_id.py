# -*- coding: utf-8 -*-
"""
اختبار الرقم الوظيفي في التقرير
"""

def test_employee_id_fields():
    """اختبار حقول الرقم الوظيفي للموظفين"""
    print("=== اختبار حقول الرقم الوظيفي ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن الموظفين
    employees = env['hr.employee'].search([], limit=10)
    print("عدد الموظفين للاختبار: %s" % len(employees))
    print("-" * 60)
    
    for employee in employees:
        print("الموظف: %s" % employee.name)
        
        # فحص الحقول المختلفة
        fields_to_check = ['int_id', 'identification_id', 'barcode']
        
        for field_name in fields_to_check:
            if hasattr(employee, field_name):
                field_value = getattr(employee, field_name)
                status = "✓" if field_value else "✗"
                print("  %s %s: %s" % (status, field_name, field_value or "فارغ"))
            else:
                print("  ✗ %s: غير موجود" % field_name)
        
        # تحديد الرقم الوظيفي المستخدم
        employee_code = ''
        if hasattr(employee, 'int_id') and employee.int_id:
            employee_code = employee.int_id
            source = 'int_id'
        elif hasattr(employee, 'identification_id') and employee.identification_id:
            employee_code = employee.identification_id
            source = 'identification_id'
        elif hasattr(employee, 'barcode') and employee.barcode:
            employee_code = employee.barcode
            source = 'barcode'
        else:
            employee_code = str(employee.id)
            source = 'id'
        
        print("  → الرقم المستخدم في التقرير: %s (من %s)" % (employee_code, source))
        print("-" * 60)

def set_employee_int_id():
    """تعيين أرقام وظيفية للموظفين الذين لا يملكون أرقام"""
    print("=== تعيين أرقام وظيفية للموظفين ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن الموظفين بدون رقم وظيفي
    employees = env['hr.employee'].search([
        '|', 
        ('int_id', '=', False), 
        ('int_id', '=', '')
    ])
    
    print("عدد الموظفين بدون رقم وظيفي: %s" % len(employees))
    
    if not employees:
        print("جميع الموظفين لديهم أرقام وظيفية")
        return
    
    # تعيين أرقام وظيفية تلقائية
    for i, employee in enumerate(employees, 1):
        # إنشاء رقم وظيفي بناءً على ID الموظف
        new_int_id = "EMP%04d" % employee.id
        employee.int_id = new_int_id
        print("تم تعيين الرقم %s للموظف: %s" % (new_int_id, employee.name))
    
    # حفظ التغييرات
    env.cr.commit()
    print("تم حفظ التغييرات في قاعدة البيانات")

def test_leave_report_employee_code():
    """اختبار الرقم الوظيفي في تقرير الإجازات"""
    print("=== اختبار الرقم الوظيفي في تقرير الإجازات ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن إجازات
    leaves = env['hr.leave'].search([], limit=5)
    print("عدد الإجازات للاختبار: %s" % len(leaves))
    print("-" * 60)
    
    for leave in leaves:
        employee = leave.employee_id
        print("الإجازة: %s - %s" % (leave.holiday_status_id.name, employee.name))
        
        # تطبيق نفس المنطق المستخدم في التقرير
        employee_code = ''
        if hasattr(employee, 'int_id') and employee.int_id:
            employee_code = employee.int_id
            source = 'int_id'
        elif hasattr(employee, 'identification_id') and employee.identification_id:
            employee_code = employee.identification_id
            source = 'identification_id'
        elif hasattr(employee, 'barcode') and employee.barcode:
            employee_code = employee.barcode
            source = 'barcode'
        else:
            employee_code = str(employee.id)
            source = 'id'
        
        print("  الرقم الوظيفي في التقرير: %s (من %s)" % (employee_code, source))
        print("-" * 60)

# تشغيل الاختبارات
if __name__ == "__main__":
    print("استخدم الدوال التالية:")
    print("1. test_employee_id_fields() - لفحص حقول الرقم الوظيفي")
    print("2. set_employee_int_id() - لتعيين أرقام وظيفية للموظفين")
    print("3. test_leave_report_employee_code() - لاختبار الرقم في التقرير")
    print("\nمثال:")
    print("exec(open('/path/to/hr_leave_reports/test_employee_id.py').read())")
    print("test_employee_id_fields()")
