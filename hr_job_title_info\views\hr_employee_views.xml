<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Inherit Employee Form View to Add Job Title Info Tab -->
        <record id="view_employee_form_job_title_info" model="ir.ui.view">
            <field name="name">hr.employee.form.job.title.info</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <!-- Add the new tab after HR Settings tab -->
                <xpath expr="//page[@name='hr_settings']" position="after">
                    <page name="job_title_info" string="Job Title Info" groups="hr.group_hr_user">
                        <group>
                            <group string="Job Information" name="job_info_group">
                                <field name="job_title_custom" placeholder="e.g. Senior Software Developer"/>
                                <field name="job_start_date"/>
                                <field name="job_related_department" 
                                       options="{'no_create': True, 'no_open': True}"
                                       domain="[('company_id', '=', company_id)]"/>
                                <field name="job_level"/>
                            </group>
                            <group string="Job Details" name="job_details_group">
                                <!-- Empty group for better layout -->
                            </group>
                        </group>
                        
                        <group string="Responsibilities &amp; Notes" name="responsibilities_group">
                            <field name="job_responsibilities" 
                                   placeholder="Describe the main responsibilities and duties for this position..."
                                   nolabel="1"/>
                        </group>
                        
                        <group string="Additional Information" name="additional_info_group">
                            <field name="job_additional_notes" 
                                   placeholder="Any additional notes, comments, or special requirements..."
                                   nolabel="1"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Add fields to tree view for better visibility -->
        <record id="view_employee_tree_job_title_info" model="ir.ui.view">
            <field name="name">hr.employee.tree.job.title.info</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='job_id']" position="after">
                    <field name="job_title_custom" optional="hide" string="Custom Job Title"/>
                    <field name="job_level" optional="hide"/>
                    <field name="job_start_date" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Add search filters -->
        <record id="view_employee_filter_job_title_info" model="ir.ui.view">
            <field name="name">hr.employee.search.job.title.info</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='job_id']" position="after">
                    <field name="job_title_custom" string="Custom Job Title"/>
                    <field name="job_level" string="Job Level"/>
                    <field name="job_related_department" string="Related Department"/>
                </xpath>
                <xpath expr="//filter[@name='group_department']" position="after">
                    <filter name="group_job_level" string="Job Level" 
                            domain="[]" context="{'group_by': 'job_level'}"/>
                    <filter name="group_related_department" string="Related Department" 
                            domain="[]" context="{'group_by': 'job_related_department'}"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
