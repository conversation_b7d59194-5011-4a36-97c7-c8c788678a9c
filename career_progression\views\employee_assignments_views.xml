<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Employee Assignment Tree View -->
        <record id="view_employee_assignment_tree" model="ir.ui.view">
            <field name="name">employee.assignment.tree</field>
            <field name="model">employee.assignment</field>
            <field name="arch" type="xml">
                <tree string="Assignments and Tasks"
                      decoration-info="state=='assigned'"
                      decoration-warning="state=='in_progress'"
                      decoration-success="state=='completed'"
                      decoration-muted="state=='cancelled'"
                      decoration-danger="is_overdue==True">
                    <field name="start_date"/>
                    <field name="title"/>
                    <field name="assignment_type"/>
                    <field name="employee_id"/>
                    <field name="assigned_by"/>
                    <field name="end_date"/>
                    <field name="priority" widget="priority"/>
                    <field name="progress_percentage" widget="progressbar"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='assigned'"
                           decoration-warning="state=='in_progress'"
                           decoration-success="state=='completed'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="is_overdue" invisible="1"/>
                </tree>
            </field>
        </record>

        <!-- Employee Assignment Form View -->
        <record id="view_employee_assignment_form" model="ir.ui.view">
            <field name="name">employee.assignment.form</field>
            <field name="model">employee.assignment</field>
            <field name="arch" type="xml">
                <form string="Assignment">
                    <header>
                        <button name="action_start" string="Start" type="object"
                                class="oe_highlight" attrs="{'invisible': [('state', '!=', 'assigned')]}"/>
                        <button name="action_complete" string="Complete" type="object"
                                class="oe_highlight" attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                        <button name="action_hold" string="Put on Hold" type="object"
                                attrs="{'invisible': [('state', 'not in', ['assigned', 'in_progress'])]}"/>
                        <button name="action_resume" string="Resume" type="object"
                                class="oe_highlight" attrs="{'invisible': [('state', '!=', 'on_hold')]}"/>
                        <button name="action_cancel" string="Cancel" type="object"
                                attrs="{'invisible': [('state', 'in', ['completed', 'cancelled'])]}"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="assigned,in_progress,completed"/>
                    </header>
                    <sheet>

                        <div class="oe_title">
                            <h1>
                                <field name="title" placeholder="Assignment Title"/>
                            </h1>
                        </div>

                        <group>
                            <group string="Basic Information">
                                <field name="employee_id" options="{'no_create': True}"/>
                                <field name="assignment_type"/>
                                <field name="priority"/>
                                <field name="assigned_by" options="{'no_create': True}"/>
                                <field name="department_id" options="{'no_create': True}"/>
                            </group>
                            <group string="Timeline">
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field name="duration_days" readonly="1"/>
                                <field name="progress_percentage" widget="progressbar"/>
                                <field name="is_overdue" readonly="1"/>
                            </group>
                        </group>

                        <notebook>
                            <page name="description" string="Description">
                                <group>
                                    <field name="description" nolabel="1"
                                           placeholder="Detailed description of the assignment..."/>
                                </group>
                            </page>

                            <page name="objectives" string="Objectives">
                                <group>
                                    <field name="objectives" nolabel="1"
                                           placeholder="Objectives and expected outcomes..."/>
                                </group>
                            </page>

                            <page name="training_details" string="Training Details"
                                  attrs="{'invisible': [('assignment_type', 'not in', ['internal_training', 'external_training'])]}">
                                <group>
                                    <group string="Training Information">
                                        <field name="training_provider"/>
                                        <field name="training_location"/>
                                        <field name="training_hours"/>
                                    </group>
                                    <group string="Completion">
                                        <field name="certificate_received"/>
                                    </group>
                                </group>
                            </page>

                            <page name="completion" string="Completion Notes">
                                <group>
                                    <field name="completion_notes" nolabel="1"
                                           placeholder="Notes about completion, results, or feedback..."/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Employee Assignment Kanban View -->
        <record id="view_employee_assignment_kanban" model="ir.ui.view">
            <field name="name">employee.assignment.kanban</field>
            <field name="model">employee.assignment</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state" class="o_kanban_small_column">
                    <field name="title"/>
                    <field name="employee_id"/>
                    <field name="assignment_type"/>
                    <field name="priority"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="progress_percentage"/>
                    <field name="state"/>
                    <field name="is_overdue"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="oe_kanban_details">
                                        <strong class="o_kanban_record_title">
                                            <field name="title"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="employee_id"/>
                                        </div>
                                        <div class="o_kanban_record_body">
                                            <field name="assignment_type" widget="badge"/>
                                            <br/>
                                            <t t-if="record.start_date.raw_value">
                                                <strong>Start:</strong> <field name="start_date"/>
                                            </t>
                                            <t t-if="record.end_date.raw_value">
                                                <br/><strong>End:</strong> <field name="end_date"/>
                                            </t>
                                            <t t-if="record.progress_percentage.raw_value">
                                                <br/><strong>Progress:</strong> <field name="progress_percentage"/>%
                                            </t>
                                        </div>
                                    </div>
                                    <div class="oe_kanban_footer">
                                        <div class="oe_kanban_footer_left">
                                            <field name="priority" widget="priority"/>
                                        </div>
                                        <div class="oe_kanban_footer_right">
                                            <t t-if="record.is_overdue.raw_value">
                                                <span class="badge badge-danger">Overdue</span>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Employee Assignment Calendar View -->
        <record id="view_employee_assignment_calendar" model="ir.ui.view">
            <field name="name">employee.assignment.calendar</field>
            <field name="model">employee.assignment</field>
            <field name="arch" type="xml">
                <calendar string="Assignments Calendar"
                          date_start="start_date"
                          date_stop="end_date"
                          color="assignment_type"
                          quick_add="False">
                    <field name="title"/>
                    <field name="employee_id"/>
                    <field name="assignment_type"/>
                    <field name="state"/>
                </calendar>
            </field>
        </record>

        <!-- Employee Assignment Search View -->
        <record id="view_employee_assignment_search" model="ir.ui.view">
            <field name="name">employee.assignment.search</field>
            <field name="model">employee.assignment</field>
            <field name="arch" type="xml">
                <search string="Search Assignments">
                    <field name="title"/>
                    <field name="employee_id"/>
                    <field name="assigned_by"/>
                    <field name="department_id"/>
                    <separator/>
                    <filter name="my_assignments" string="My Assignments"
                            domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter name="assigned_by_me" string="Assigned by Me"
                            domain="[('assigned_by.user_id', '=', uid)]"/>
                    <separator/>
                    <filter name="active" string="Active"
                            domain="[('state', 'in', ['assigned', 'in_progress'])]"/>
                    <filter name="completed" string="Completed"
                            domain="[('state', '=', 'completed')]"/>
                    <filter name="overdue" string="Overdue"
                            domain="[('is_overdue', '=', True)]"/>
                    <separator/>
                    <filter name="tasks" string="Tasks"
                            domain="[('assignment_type', '=', 'task')]"/>
                    <filter name="assignments" string="Assignments"
                            domain="[('assignment_type', '=', 'assignment')]"/>
                    <filter name="training" string="Training"
                            domain="[('assignment_type', 'in', ['internal_training', 'external_training'])]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter name="group_employee" string="Employee"
                                context="{'group_by': 'employee_id'}"/>
                        <filter name="group_type" string="Type"
                                context="{'group_by': 'assignment_type'}"/>
                        <filter name="group_state" string="Status"
                                context="{'group_by': 'state'}"/>
                        <filter name="group_department" string="Department"
                                context="{'group_by': 'department_id'}"/>
                        <filter name="group_assigned_by" string="Assigned By"
                                context="{'group_by': 'assigned_by'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Employee Assignment Action -->
        <record id="action_employee_assignment" model="ir.actions.act_window">
            <field name="name">Assignments and Tasks</field>
            <field name="res_model">employee.assignment</field>
            <field name="view_mode">kanban,tree,form,calendar</field>
            <field name="search_view_id" ref="view_employee_assignment_search"/>
            <field name="context">{
                'search_default_active': 1,
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first assignment or task!
                </p>
                <p>
                    Track assignments, tasks, training, and other activities for employees.
                    Monitor progress, deadlines, and completion status.
                </p>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_employee_assignments_root"
                  name="Assignments and Tasks"
                  parent="hr.menu_hr_root"
                  sequence="15"
                  groups="hr.group_hr_user"/>

        <menuitem id="menu_employee_assignments"
                  name="All Assignments"
                  parent="menu_employee_assignments_root"
                  action="action_employee_assignment"
                  sequence="10"/>

        <!-- My Assignments Action -->
        <record id="action_my_assignments" model="ir.actions.act_window">
            <field name="name">My Assignments</field>
            <field name="res_model">employee.assignment</field>
            <field name="view_mode">kanban,tree,form,calendar</field>
            <field name="search_view_id" ref="view_employee_assignment_search"/>
            <field name="domain">[('employee_id.user_id', '=', uid)]</field>
            <field name="context">{
                'search_default_active': 1,
            }</field>
        </record>

        <menuitem id="menu_my_assignments"
                  name="My Assignments"
                  parent="menu_employee_assignments_root"
                  action="action_my_assignments"
                  sequence="5"/>

    </data>
</odoo>
