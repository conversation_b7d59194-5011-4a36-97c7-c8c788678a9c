# دليل الترجمة الشاملة لمديول BSIC Requests
# Complete Translation Guide for BSIC Requests Module

## نظرة عامة / Overview

تم إنشاء دعم ترجمة شامل ومحدث لمديول `bssic_requests` ليدعم اللغة العربية والإنجليزية بشكل كامل.
Complete and updated translation support has been created for the `bssic_requests` module to fully support Arabic and English languages.

## الملفات المحدثة / Updated Files

### ملفات الترجمة / Translation Files
- `i18n/ar.po` - ملف الترجمة العربية الشامل / Complete Arabic translation file
- `i18n/en.po` - ملف الترجمة الإنجليزية الشامل / Complete English translation file

## التحديثات الجديدة / New Updates

### ✅ ترجمة شاملة / Complete Translation
- تمت ترجمة جميع النصوص في المديول
- تغطية كاملة لجميع أنواع الطلبات
- ترجمة جميع الحقول والأزرار والقوائم
- ترجمة رسائل الخطأ والتحقق
- ترجمة نصوص المساعدة والإرشادات

### ✅ أنواع الطلبات المترجمة / Translated Request Types
- Password Reset → إعادة تعيين كلمة المرور
- USB Access → الوصول إلى USB
- Extension Request → طلب تمديد
- Permission Request → طلب صلاحية
- Email Request → طلب بريد إلكتروني
- Technical Request → طلب تقني
- Authorization Delegation → تفويض صلاحية في سقف المستخدم
- Free Form → القيد الحر

### ✅ حالات سير العمل / Workflow States
- Draft → مسودة
- Submitted → مُقدم
- Direct Manager Approval → موافقة المدير المباشر
- Audit Manager Approval → موافقة مدير التدقيق
- IT Manager Approval → موافقة مدير تقنية المعلومات
- Assigned to IT Staff → مُعين لموظف تقنية المعلومات
- In Progress → قيد التنفيذ
- Completed → مكتمل
- Rejected → مرفوض

### ✅ الأزرار والإجراءات / Buttons and Actions
- Submit → إرسال
- Approve (Direct Manager) → موافقة (المدير المباشر)
- Approve (Audit Manager) → موافقة (مدير التدقيق)
- Approve (IT Manager) → موافقة (مدير تقنية المعلومات)
- Assign to IT Staff → تعيين لموظف تقنية المعلومات
- Mark In Progress → تحديد كقيد التنفيذ
- Mark Completed → تحديد كمكتمل
- Reject → رفض

### ✅ الحقول الأساسية / Basic Fields
- Request Reference → مرجع الطلب
- Employee → الموظف
- Employee Number (ID) → رقم الموظف (الهوية)
- Department → القسم
- Job Position → المنصب الوظيفي
- Request Type → نوع الطلب
- Request Date → تاريخ الطلب
- Description → الوصف
- Status → الحالة
- Priority → الأولوية

### ✅ حقول طلبات إعادة تعيين كلمة المرور / Password Reset Fields
- Username → اسم المستخدم
- Device Type → نوع الجهاز
- Internet → الإنترنت
- System → النظام
- Swift → سويفت
- Other → أخرى
- Request Reason → سبب الطلب

### ✅ حقول طلبات USB / USB Request Fields
- Purpose of USB Usage → الغرض من استخدام USB
- Required Duration → المدة المطلوبة
- Type of Data to Transfer → نوع البيانات المراد نقلها

### ✅ حقول طلبات التمديد / Extension Request Fields
- Required Extension Period → فترة التمديد المطلوبة
- Reason for Extension → سبب التمديد

### ✅ حقول طلبات الصلاحيات / Permission Request Fields
- Permission Type → نوع الصلاحية
- Add → إضافة
- Modify → تعديل
- Delete → حذف
- Withdraw → سحب
- Activate → تفعيل
- Deactivate → إلغاء تفعيل
- Valid From → صالح من
- Valid To → صالح إلى

### ✅ حقول طلبات البريد الإلكتروني / Email Request Fields
- Email Type → نوع البريد الإلكتروني
- New Email Account → حساب بريد إلكتروني جديد
- Email Modification → تعديل البريد الإلكتروني
- Requested Email → البريد الإلكتروني المطلوب
- Purpose → الغرض

### ✅ حقول طلبات تفويض الصلاحية / Authorization Delegation Fields
- Reason for Ceiling Increase → سبب رفع السقف
- Details → التفاصيل
- From Date → التاريخ من
- To Date → التاريخ إلى
- Max. Amount → الحد الأقصى للمبلغ
- Auth O.D. Limit → حد التفويض

### ✅ حقول طلبات القيد الحر / Free Form Fields
- Subject → الموضوع
- Operation Details → تفاصيل العملية
- Entry Type → نوع القيد
- Free Entry → القيد الحر
- Reverse Entry → عكس قيد

### ✅ الحقول التقنية / Technical Fields
- Category → الفئة
- Subcategory → الفئة الفرعية
- Low → منخفضة
- Normal → عادية
- High → عالية
- Urgent → عاجلة

### ✅ المجموعات الأمنية / Security Groups
- Employee → موظف
- Direct Manager → المدير المباشر
- Audit Manager → مدير التدقيق
- IT Manager → مدير تقنية المعلومات
- IT Staff → موظف تقنية المعلومات

## كيفية تفعيل الترجمة / How to Enable Translation

### 1. تثبيت اللغة العربية / Install Arabic Language
1. اذهب إلى الإعدادات → اللغات / Go to Settings → Languages
2. انقر على "تحميل لغة" / Click "Load a Language"
3. اختر "العربية" / Select "Arabic"
4. انقر على "تحميل" / Click "Load"

### 2. تفعيل الترجمة للمستخدم / Enable Translation for User
1. اذهب إلى الإعدادات → المستخدمون / Go to Settings → Users
2. اختر المستخدم المطلوب / Select the desired user
3. في تبويب "التفضيلات" / In "Preferences" tab
4. اختر "العربية" من قائمة اللغة / Select "Arabic" from Language dropdown
5. احفظ التغييرات / Save changes

### 3. تحديث المديول / Update Module
```bash
# من مجلد Odoo الرئيسي / From main Odoo directory
python odoo-bin -d your_database -u bssic_requests --stop-after-init
```

## اختبار الترجمة / Testing Translation

### 1. تغيير اللغة / Change Language
1. انقر على اسم المستخدم في الزاوية العلوية اليمنى
2. اختر "التفضيلات" / "Preferences"
3. غيّر اللغة إلى "العربية" أو "English"
4. احفظ التغييرات

### 2. التحقق من الترجمة / Verify Translation
- ✅ القوائم الرئيسية والفرعية
- ✅ أسماء الحقول وتسمياتها
- ✅ الأزرار والإجراءات
- ✅ حالات سير العمل
- ✅ رسائل الخطأ والتحقق
- ✅ نصوص المساعدة
- ✅ عناوين العروض والنماذج

## الميزات الجديدة / New Features

### ✅ ترجمة ديناميكية / Dynamic Translation
- تبديل فوري بين اللغات
- عرض متسق في جميع أجزاء المديول
- دعم كامل للنصوص من اليمين إلى اليسار (RTL)

### ✅ تغطية شاملة / Complete Coverage
- 100% من النصوص المرئية للمستخدم
- جميع أنواع الطلبات
- جميع حالات سير العمل
- جميع رسائل النظام

## إضافة ترجمات جديدة / Adding New Translations

لإضافة ترجمات جديدة:
To add new translations:

1. عدّل ملف `i18n/ar.po` للعربية
2. عدّل ملف `i18n/en.po` للإنجليزية
3. أعد تشغيل الخادم أو حدّث المديول

## الدعم / Support

إذا واجهت مشاكل في الترجمة، تأكد من:
If you encounter translation issues, make sure:

1. تم تثبيت اللغة العربية بشكل صحيح
2. تم تحديد اللغة للمستخدم
3. تم إعادة تشغيل الخادم بعد تحديث الترجمات
4. ملفات الترجمة موجودة في مجلد `i18n/`

## ملاحظات / Notes

- الترجمة تعمل تلقائياً حسب لغة المستخدم
- يمكن للمستخدمين المختلفين استخدام لغات مختلفة
- الترجمة تشمل جميع النصوص المرئية للمستخدم
- رسائل الخطأ والتحقق مترجمة أيضاً
