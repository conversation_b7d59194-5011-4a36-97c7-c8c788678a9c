<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Career Progression Events -->
        <record id="demo_career_event_1" model="career.progression">
            <field name="name"><PERSON> - Initial Hiring</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="event_date" eval="(DateTime.now() - timedelta(days=730)).strftime('%Y-%m-%d')"/>
            <field name="event_type">other</field>
            <!-- <field name="new_job_id">Reference to hr.job record</field> -->
            <field name="new_department_id" ref="hr.dep_rd"/>
            <field name="reason">Initial hiring as Junior Software Developer</field>
            <field name="state">confirmed</field>
        </record>

        <record id="demo_career_event_2" model="career.progression">
            <field name="name"><PERSON> - <PERSON> to Senior Developer</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="event_date" eval="(DateTime.now() - timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="event_type">promotion</field>
            <field name="previous_job_title">Junior Software Developer</field>
            <!-- <field name="new_job_id">Reference to hr.job record</field> -->
            <field name="previous_department_id" ref="hr.dep_rd"/>
            <field name="new_department_id" ref="hr.dep_rd"/>
            <field name="reason">Excellent performance and technical skills demonstrated over the past year</field>
            <field name="state">confirmed</field>
        </record>

        <record id="demo_career_event_3" model="career.progression">
            <field name="name">Ahmed Hassan - Temporary Team Lead Assignment</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="event_date" eval="(DateTime.now() - timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="event_type">temporary_assignment</field>
            <field name="previous_job_title">Senior Software Developer</field>
            <!-- <field name="new_job_id">Reference to hr.job record</field> -->
            <field name="previous_department_id" ref="hr.dep_rd"/>
            <field name="new_department_id" ref="hr.dep_rd"/>
            <field name="is_temporary">True</field>
            <field name="temporary_start_date" eval="(DateTime.now() - timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="temporary_end_date" eval="(DateTime.now() - timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="reason">Temporary assignment to lead the mobile app development project</field>
            <field name="state">confirmed</field>
        </record>

        <record id="demo_career_event_4" model="career.progression">
            <field name="name">Ahmed Hassan - Promotion to Team Lead</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="event_date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="event_type">promotion</field>
            <field name="previous_job_title">Senior Software Developer</field>
            <!-- <field name="new_job_id">Reference to hr.job record</field> -->
            <field name="previous_department_id" ref="hr.dep_rd"/>
            <field name="new_department_id" ref="hr.dep_rd"/>
            <field name="reason">Successful completion of temporary team lead assignment and demonstrated leadership skills</field>
            <field name="state">confirmed</field>
        </record>

        <!-- Demo event for another employee if exists -->
        <record id="demo_career_event_5" model="career.progression">
            <field name="name">Employee Transfer to Sales Department</field>
            <field name="employee_id" ref="hr.employee_admin"/>
            <field name="event_date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="event_type">transfer</field>
            <field name="previous_job_title">Development Team Lead</field>
            <!-- <field name="new_job_id">Reference to hr.job record</field> -->
            <field name="previous_department_id" ref="hr.dep_rd"/>
            <field name="new_department_id" ref="hr.dep_sales"/>
            <field name="reason">Strategic move to leverage technical expertise in sales role</field>
            <field name="notes">This transfer will help bridge the gap between technical and sales teams</field>
            <field name="state">draft</field>
        </record>

    </data>
</odoo>
