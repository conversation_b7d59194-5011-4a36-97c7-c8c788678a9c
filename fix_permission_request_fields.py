#!/usr/bin/env python3
"""
سكريبت لإصلاح مشكلة عدم ظهور تفاصيل طلبات الصلاحيات
Script to fix permission request fields not showing issue
"""

import sys
import os

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

def fix_permission_request_type():
    """إصلاح إعدادات نوع طلب الصلاحيات"""
    try:
        # Import Odoo modules
        import odoo
        from odoo import api, SUPERUSER_ID
        
        # Initialize Odoo
        odoo.tools.config.parse_config([])
        
        # Get database name from config or use default
        db_name = odoo.tools.config['db_name'] or 'your_database_name'
        
        # Create registry and environment
        registry = odoo.registry(db_name)
        
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            print("🔍 فحص إعدادات نوع طلب الصلاحيات...")
            print("🔍 Checking permission request type settings...")
            
            # البحث عن نوع طلب الصلاحيات
            permission_type = env['bssic.request.type'].search([
                ('code', '=', 'permission')
            ], limit=1)
            
            if not permission_type:
                print("❌ لم يتم العثور على نوع طلب الصلاحيات!")
                print("❌ Permission request type not found!")
                return False
            
            print(f"✅ تم العثور على نوع الطلب: {permission_type.name}")
            print(f"✅ Found request type: {permission_type.name}")
            
            # فحص الإعدادات الحالية
            print(f"📋 الإعدادات الحالية / Current settings:")
            print(f"   - show_permission_fields: {permission_type.show_permission_fields}")
            print(f"   - active: {permission_type.active}")
            print(f"   - code: {permission_type.code}")
            
            # إصلاح الإعدادات إذا لزم الأمر
            needs_update = False
            update_vals = {}
            
            if not permission_type.show_permission_fields:
                update_vals['show_permission_fields'] = True
                needs_update = True
                print("🔧 سيتم تفعيل show_permission_fields")
                print("🔧 Will enable show_permission_fields")
            
            if not permission_type.active:
                update_vals['active'] = True
                needs_update = True
                print("🔧 سيتم تفعيل النوع")
                print("🔧 Will activate the type")
            
            if needs_update:
                permission_type.write(update_vals)
                print("✅ تم تحديث الإعدادات بنجاح!")
                print("✅ Settings updated successfully!")
            else:
                print("✅ الإعدادات صحيحة بالفعل")
                print("✅ Settings are already correct")
            
            # فحص الطلبات الموجودة
            print("\n🔍 فحص الطلبات الموجودة...")
            print("🔍 Checking existing requests...")
            
            permission_requests = env['bssic.request'].search([
                ('request_type_id.code', '=', 'permission')
            ])
            
            print(f"📊 عدد طلبات الصلاحيات الموجودة: {len(permission_requests)}")
            print(f"📊 Number of existing permission requests: {len(permission_requests)}")
            
            if permission_requests:
                # فحص عينة من الطلبات
                sample_request = permission_requests[0]
                print(f"\n📋 فحص طلب عينة (ID: {sample_request.id}):")
                print(f"📋 Checking sample request (ID: {sample_request.id}):")
                print(f"   - show_permission_fields: {sample_request.show_permission_fields}")
                print(f"   - request_type_id: {sample_request.request_type_id.name}")
                print(f"   - permission_type: {sample_request.permission_type}")
                print(f"   - user_name: {sample_request.user_name}")
                
                # إعادة حساب الحقول المحسوبة
                print("\n🔄 إعادة حساب الحقول المحسوبة...")
                print("🔄 Recomputing computed fields...")
                permission_requests._compute_show_fields()
                
                print("✅ تم إعادة حساب الحقول")
                print("✅ Fields recomputed")
            
            # حفظ التغييرات
            cr.commit()
            print("\n✅ تم حفظ جميع التغييرات بنجاح!")
            print("✅ All changes saved successfully!")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        print(f"❌ Error: {str(e)}")
        return False

def check_view_configuration():
    """فحص إعدادات العرض"""
    print("\n🔍 فحص إعدادات العرض...")
    print("🔍 Checking view configuration...")
    
    try:
        import odoo
        from odoo import api, SUPERUSER_ID
        
        db_name = odoo.tools.config['db_name'] or 'your_database_name'
        registry = odoo.registry(db_name)
        
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # فحص العرض الرئيسي
            main_view = env.ref('bssic_requests.view_bssic_request_form', raise_if_not_found=False)
            if main_view:
                print(f"✅ العرض الرئيسي موجود: {main_view.name}")
                print(f"✅ Main view exists: {main_view.name}")
            else:
                print("❌ العرض الرئيسي غير موجود!")
                print("❌ Main view not found!")
            
            # فحص عرض طلبات الصلاحيات
            permission_view = env.ref('bssic_requests.view_bssic_permission_request_form_new', raise_if_not_found=False)
            if permission_view:
                print(f"✅ عرض طلبات الصلاحيات موجود: {permission_view.name}")
                print(f"✅ Permission request view exists: {permission_view.name}")
            else:
                print("❌ عرض طلبات الصلاحيات غير موجود!")
                print("❌ Permission request view not found!")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص العرض: {str(e)}")
        print(f"❌ Error checking view: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح مشكلة طلبات الصلاحيات...")
    print("🚀 Starting permission request fix...")
    
    # إصلاح نوع الطلب
    if fix_permission_request_type():
        print("\n✅ تم إصلاح نوع الطلب بنجاح")
        print("✅ Request type fixed successfully")
    else:
        print("\n❌ فشل في إصلاح نوع الطلب")
        print("❌ Failed to fix request type")
    
    # فحص إعدادات العرض
    check_view_configuration()
    
    print("\n🎉 انتهى السكريبت!")
    print("🎉 Script completed!")
