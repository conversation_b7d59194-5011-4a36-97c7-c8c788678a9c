"""
سكريبت بسيط لإصلاح مشكلة عدم ظهور حقول طلبات الصلاحيات
Simple script to fix permission request fields visibility issue

يمكن تشغيل هذا السكريبت من خلال:
1. Odoo shell: python odoo-bin shell -d your_database_name --addons-path=your_addons_path
2. أو من خلال Developer Mode في Odoo

This script can be run via:
1. Odoo shell: python odoo-bin shell -d your_database_name --addons-path=your_addons_path  
2. Or through Developer Mode in Odoo
"""

def fix_permission_request_fields():
    """إصلاح مشكلة عدم ظهور حقول طلبات الصلاحيات"""
    
    print("🔍 بدء فحص وإصلاح طلبات الصلاحيات...")
    print("🔍 Starting permission request fields fix...")
    
    try:
        # 1. فحص وإصلاح نوع الطلب
        print("\n📋 فحص نوع طلب الصلاحيات...")
        print("📋 Checking permission request type...")
        
        permission_type = env['bssic.request.type'].search([
            ('code', '=', 'permission')
        ], limit=1)
        
        if not permission_type:
            print("❌ نوع طلب الصلاحيات غير موجود!")
            print("❌ Permission request type not found!")
            return False
        
        print(f"✅ تم العثور على: {permission_type.name}")
        print(f"✅ Found: {permission_type.name}")
        print(f"   - show_permission_fields: {permission_type.show_permission_fields}")
        print(f"   - active: {permission_type.active}")
        
        # إصلاح الإعدادات
        if not permission_type.show_permission_fields or not permission_type.active:
            permission_type.write({
                'show_permission_fields': True,
                'active': True
            })
            print("✅ تم تحديث إعدادات نوع الطلب")
            print("✅ Request type settings updated")
        
        # 2. فحص الطلبات الموجودة
        print("\n📊 فحص الطلبات الموجودة...")
        print("📊 Checking existing requests...")
        
        permission_requests = env['bssic.request'].search([
            ('request_type_id.code', '=', 'permission')
        ])
        
        print(f"📈 عدد طلبات الصلاحيات: {len(permission_requests)}")
        print(f"📈 Number of permission requests: {len(permission_requests)}")
        
        if permission_requests:
            # إعادة حساب الحقول المحسوبة
            print("🔄 إعادة حساب الحقول المحسوبة...")
            print("🔄 Recomputing computed fields...")
            
            permission_requests._compute_show_fields()
            
            # فحص عينة
            sample = permission_requests[0]
            print(f"\n📋 فحص طلب عينة (ID: {sample.id}):")
            print(f"📋 Sample request check (ID: {sample.id}):")
            print(f"   - show_permission_fields: {sample.show_permission_fields}")
            print(f"   - request_type_id: {sample.request_type_id.name if sample.request_type_id else 'None'}")
            
            if sample.show_permission_fields:
                print("✅ الحقول ستظهر الآن!")
                print("✅ Fields should now be visible!")
            else:
                print("⚠️ الحقول ما زالت مخفية - قد تحتاج لإعادة تحميل الصفحة")
                print("⚠️ Fields still hidden - may need page refresh")
        
        # 3. إنشاء طلب تجريبي للاختبار
        print("\n🧪 إنشاء طلب تجريبي...")
        print("🧪 Creating test request...")
        
        # البحث عن موظف للاختبار
        test_employee = env['hr.employee'].search([], limit=1)
        if test_employee:
            test_request = env['bssic.request'].create({
                'name': 'Test Permission Request',
                'employee_id': test_employee.id,
                'request_type_id': permission_type.id,
                'request_type_code': 'permission',
                'description': 'Test request to verify fields visibility'
            })
            
            print(f"✅ تم إنشاء طلب تجريبي (ID: {test_request.id})")
            print(f"✅ Test request created (ID: {test_request.id})")
            print(f"   - show_permission_fields: {test_request.show_permission_fields}")
            
            # حذف الطلب التجريبي
            test_request.unlink()
            print("🗑️ تم حذف الطلب التجريبي")
            print("🗑️ Test request deleted")
        
        print("\n🎉 تم الانتهاء من الإصلاح!")
        print("🎉 Fix completed!")
        print("\n📝 الخطوات التالية:")
        print("📝 Next steps:")
        print("   1. قم بإعادة تحميل صفحة Odoo")
        print("   1. Refresh your Odoo page")
        print("   2. افتح طلب صلاحيات موجود")
        print("   2. Open an existing permission request")
        print("   3. تحقق من ظهور التفاصيل في تبويب 'Request Details'")
        print("   3. Check if details appear in 'Request Details' tab")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# تشغيل الإصلاح
if __name__ == "__main__":
    # إذا كان يتم تشغيله من Odoo shell
    if 'env' in globals():
        fix_permission_request_fields()
    else:
        print("⚠️ يجب تشغيل هذا السكريبت من Odoo shell")
        print("⚠️ This script must be run from Odoo shell")
        print("python odoo-bin shell -d your_database_name")

# للتشغيل المباشر في Developer Mode أو Odoo shell:
# exec(open('fix_permission_fields_simple.py').read())
