<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo data for Job Title Info -->
        <record id="hr.employee_admin" model="hr.employee">
            <field name="job_title_custom">System Administrator</field>
            <field name="job_start_date">2023-01-15</field>
            <field name="job_level">senior</field>
            <field name="job_responsibilities">Manage and maintain IT infrastructure, ensure system security, provide technical support to users, and implement new technologies.</field>
            <field name="job_additional_notes">Certified in multiple technologies. Excellent problem-solving skills.</field>
        </record>

        <record id="hr.employee_manager" model="hr.employee">
            <field name="job_title_custom">HR Manager</field>
            <field name="job_start_date">2022-03-01</field>
            <field name="job_level">manager</field>
            <field name="job_responsibilities">Oversee HR operations, manage recruitment processes, handle employee relations, and ensure compliance with labor laws.</field>
            <field name="job_additional_notes">MBA in Human Resources. 10+ years of experience in HR management.</field>
        </record>

        <record id="hr.employee_developer" model="hr.employee">
            <field name="job_title_custom">Senior Software Developer</field>
            <field name="job_start_date">2023-06-01</field>
            <field name="job_level">senior</field>
            <field name="job_responsibilities">Design and develop software applications, code review, mentor junior developers, and participate in architectural decisions.</field>
            <field name="job_additional_notes">Expert in Python, JavaScript, and database design. Strong leadership skills.</field>
        </record>

        <record id="hr.employee_accountant" model="hr.employee">
            <field name="job_title_custom">Senior Accountant</field>
            <field name="job_start_date">2022-09-15</field>
            <field name="job_level">senior</field>
            <field name="job_responsibilities">Prepare financial statements, manage accounts payable/receivable, conduct financial analysis, and ensure compliance with accounting standards.</field>
            <field name="job_additional_notes">CPA certified. Specialized in financial reporting and tax preparation.</field>
        </record>

        <record id="hr.employee_sales" model="hr.employee">
            <field name="job_title_custom">Sales Representative</field>
            <field name="job_start_date">2023-08-01</field>
            <field name="job_level">mid</field>
            <field name="job_responsibilities">Generate leads, manage client relationships, achieve sales targets, and provide product demonstrations to potential customers.</field>
            <field name="job_additional_notes">Excellent communication skills. Consistently exceeds sales targets.</field>
        </record>

    </data>
</odoo>
