# دليل تثبيت موديول Career Progression

## متطلبات النظام

### الإصدارات المطلوبة
- **Odoo**: 15.0 أو أحدث
- **Python**: 3.8 أو أحدث
- **PostgreSQL**: 12 أو أحدث

### الموديولات المطلوبة
- `hr` (الموارد البشرية الأساسي) - مثبت افتراضياً
- `mail` (نظام الرسائل) - مثبت افتراضياً

## خطوات التثبيت

### 1. نسخ الملفات
```bash
# انسخ مجلد career_progression إلى مجلد addons في Odoo
cp -r career_progression /path/to/odoo/addons/
```

### 2. إعادة تشغيل خدمة Odoo
```bash
# أعد تشغيل خدمة Odoo لتحميل الموديول الجديد
sudo systemctl restart odoo
# أو
sudo service odoo restart
```

### 3. تحديث قائمة التطبيقات
1. سجل دخولك إلى Odoo كمدير نظام
2. انتقل إلى **Apps** (التطبيقات)
3. اضغط على **Update Apps List** (تحديث قائمة التطبيقات)
4. أكد التحديث

### 4. تثبيت الموديول
1. في قائمة التطبيقات، ابحث عن "Career Progression"
2. اضغط على **Install** (تثبيت)
3. انتظر حتى اكتمال التثبيت

## التحقق من التثبيت

### 1. فحص القوائم
بعد التثبيت الناجح، ستجد:
- قائمة جديدة "Career Progression" في قائمة الموارد البشرية
- تبويب "Career Path" في نموذج الموظف

### 2. فحص الصلاحيات
تأكد من أن المستخدمين لديهم الصلاحيات المناسبة:
- **HR Officer**: قراءة وكتابة
- **HR Manager**: صلاحيات كاملة
- **Employee**: قراءة فقط

### 3. اختبار الوظائف الأساسية
1. افتح ملف موظف
2. انتقل إلى تبويب "Career Path"
3. أضف حدث وظيفي جديد
4. احفظ وأكد الحدث

## إعداد البيانات الأولية

### أنواع الأحداث الوظيفية
سيتم إنشاء أنواع الأحداث التالية تلقائياً:
- النقل (Transfer)
- الترقية (Promotion)
- تعديل المسمى (Title Change)
- المهام المؤقتة (Temporary Assignment)
- تغيير الإدارة (Department Change)
- تغيير الموقع (Location Change)
- أخرى (Other)

### البيانات التجريبية
إذا كنت تريد تحميل البيانات التجريبية:
1. انتقل إلى **Settings** > **Technical** > **Database Structure** > **Demo Data**
2. ابحث عن "career_progression"
3. قم بتحميل البيانات التجريبية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. الموديول لا يظهر في قائمة التطبيقات
**الحل:**
- تأكد من نسخ الملفات في المكان الصحيح
- أعد تشغيل خدمة Odoo
- حدث قائمة التطبيقات

#### 2. خطأ في التثبيت
**الحل:**
- تحقق من سجلات Odoo للأخطاء
- تأكد من وجود الموديولات المطلوبة
- تحقق من صلاحيات الملفات

#### 3. التبويب لا يظهر في نموذج الموظف
**الحل:**
- تأكد من أن المستخدم لديه صلاحيات HR User
- أعد تحميل الصفحة
- تحقق من إعدادات العرض

#### 4. مشاكل في الصلاحيات
**الحل:**
- تحقق من ملف `ir.model.access.csv`
- تأكد من تعيين المجموعات الصحيحة للمستخدمين
- أعد تحميل الصلاحيات

### فحص السجلات
```bash
# عرض سجلات Odoo للتحقق من الأخطاء
tail -f /var/log/odoo/odoo.log
```

### اختبار قاعدة البيانات
```sql
-- التحقق من إنشاء الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_name LIKE 'career_progression%';

-- التحقق من البيانات الأساسية
SELECT * FROM career_progression_event_type;
```

## الصيانة والتحديثات

### النسخ الاحتياطي
قبل أي تحديث، تأكد من عمل نسخة احتياطية:
```bash
# نسخ احتياطي لقاعدة البيانات
pg_dump odoo_database > backup_before_update.sql
```

### تحديث الموديول
```bash
# تحديث الموديول عبر سطر الأوامر
odoo -u career_progression -d database_name
```

## الدعم الفني

### معلومات الاتصال
- **الوثائق**: راجع ملف README.md
- **الاختبارات**: تشغيل الاختبارات في مجلد tests/
- **المشاكل**: أبلغ عن المشاكل عبر نظام إدارة المشاريع

### ملفات مهمة للدعم
عند طلب الدعم، يرجى تضمين:
- سجلات Odoo
- إصدار Odoo المستخدم
- تفاصيل الخطأ
- خطوات إعادة إنتاج المشكلة

---

**ملاحظة**: هذا الموديول تم تطويره وفقاً لمعايير Odoo 15 الرسمية ويتبع أفضل الممارسات في التطوير.
