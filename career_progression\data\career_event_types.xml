<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Career Progression Event Types -->
        <record id="event_type_transfer" model="career.progression.event.type">
            <field name="name">Transfer</field>
            <field name="code">transfer</field>
            <field name="description">Employee transfer between departments or locations</field>
            <field name="sequence">10</field>
            <field name="color">1</field>
        </record>
        
        <record id="event_type_promotion" model="career.progression.event.type">
            <field name="name">Promotion</field>
            <field name="code">promotion</field>
            <field name="description">Employee promotion to higher position</field>
            <field name="sequence">20</field>
            <field name="color">3</field>
        </record>
        
        <record id="event_type_title_change" model="career.progression.event.type">
            <field name="name">Title Change</field>
            <field name="code">title_change</field>
            <field name="description">Change in job title or position name</field>
            <field name="sequence">30</field>
            <field name="color">5</field>
        </record>
        
        <record id="event_type_temporary_assignment" model="career.progression.event.type">
            <field name="name">Temporary Assignment</field>
            <field name="code">temporary_assignment</field>
            <field name="description">Temporary assignment to different role or location</field>
            <field name="sequence">40</field>
            <field name="color">7</field>
        </record>
        
        <record id="event_type_department_change" model="career.progression.event.type">
            <field name="name">Department Change</field>
            <field name="code">department_change</field>
            <field name="description">Change of department within the organization</field>
            <field name="sequence">50</field>
            <field name="color">9</field>
        </record>
        
        <record id="event_type_location_change" model="career.progression.event.type">
            <field name="name">Location Change</field>
            <field name="code">location_change</field>
            <field name="description">Change of work location or office</field>
            <field name="sequence">60</field>
            <field name="color">2</field>
        </record>
        
        <record id="event_type_other" model="career.progression.event.type">
            <field name="name">Other</field>
            <field name="code">other</field>
            <field name="description">Other career progression events</field>
            <field name="sequence">100</field>
            <field name="color">0</field>
        </record>
        
    </data>
</odoo>
