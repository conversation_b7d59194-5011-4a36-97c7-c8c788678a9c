# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class CareerProgression(models.Model):
    _name = 'career.progression'
    _description = 'Employee Career Progression'
    _order = 'event_date desc, id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Event Title',
        required=True,
        tracking=True,
        help="Brief title describing the career event"
    )

    employee_id = fields.Many2one(
        'hr.employee',
        string='Employee',
        required=True,
        ondelete='cascade',
        tracking=True,
        help="Employee for whom this career event applies"
    )

    event_date = fields.Date(
        string='Event Date',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help="Date when the career event took place"
    )

    event_type = fields.Selection([
        ('transfer', 'Transfer'),
        ('promotion', 'Promotion'),
        ('title_change', 'Title Change'),
        ('temporary_assignment', 'Temporary Assignment'),
        ('department_change', 'Department Change'),
        ('location_change', 'Location Change'),
        ('other', 'Other')
    ], string='Event Type', required=True, tracking=True,
       help="Type of career progression event")

    # Previous Position Information
    previous_job_title = fields.Char(
        string='Previous Job Title',
        tracking=True,
        readonly=True,
        help="Previous job title before this event (auto-populated from employee data)"
    )

    previous_department_id = fields.Many2one(
        'hr.department',
        string='Previous Department',
        tracking=True,
        readonly=True,
        help="Previous department before this event (auto-populated from employee data)"
    )

    # New Position Information
    new_job_id = fields.Many2one(
        'hr.job',
        string='New Job Position',
        tracking=True,
        help="New job position after this event"
    )

    new_department_id = fields.Many2one(
        'hr.department',
        string='New Department',
        tracking=True,
        help="New department after this event"
    )

    # Additional Information
    reason = fields.Text(
        string='Reason for Change',
        tracking=True,
        help="Detailed reason for this career progression event"
    )

    notes = fields.Text(
        string='Additional Notes',
        help="Any additional notes or comments about this event"
    )

    # Temporary Assignment specific fields
    is_temporary = fields.Boolean(
        string='Is Temporary Assignment',
        default=False,
        tracking=True,
        help="Check if this is a temporary assignment"
    )

    temporary_start_date = fields.Date(
        string='Temporary Start Date',
        help="Start date for temporary assignment"
    )

    temporary_end_date = fields.Date(
        string='Temporary End Date',
        help="End date for temporary assignment"
    )

    # Status and workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    # Company
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )

    @api.constrains('temporary_start_date', 'temporary_end_date')
    def _check_temporary_dates(self):
        for record in self:
            if record.is_temporary:
                if not record.temporary_start_date or not record.temporary_end_date:
                    raise ValidationError(_("Temporary assignments must have both start and end dates."))
                if record.temporary_start_date >= record.temporary_end_date:
                    raise ValidationError(_("Temporary end date must be after start date."))

    @api.constrains('event_date')
    def _check_event_date(self):
        for record in self:
            if record.event_date > fields.Date.context_today(self):
                raise ValidationError(_("Event date cannot be in the future."))

    @api.onchange('event_type')
    def _onchange_event_type(self):
        if self.event_type == 'temporary_assignment':
            self.is_temporary = True
        else:
            self.is_temporary = False
            self.temporary_start_date = False
            self.temporary_end_date = False

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        if self.employee_id:
            # Auto-populate previous information from current employee data
            self.previous_job_title = self.employee_id.job_title
            self.previous_department_id = self.employee_id.department_id

    @api.onchange('new_job_id')
    def _onchange_new_job_id(self):
        """Auto-populate new department when job position is selected"""
        if self.new_job_id:
            # Auto-populate new department from job position
            if self.new_job_id.department_id:
                self.new_department_id = self.new_job_id.department_id

    def action_confirm(self):
        """Confirm the career progression event and update employee data"""
        self.write({'state': 'confirmed'})

        # Update employee job position and department when confirmed
        for record in self:
            if record.employee_id:
                employee_vals = {}

                # Update job position if specified
                if record.new_job_id:
                    employee_vals['job_id'] = record.new_job_id.id

                # Update department if specified
                if record.new_department_id:
                    employee_vals['department_id'] = record.new_department_id.id

                # Apply updates to employee
                if employee_vals:
                    record.employee_id.write(employee_vals)

        return True

    def action_cancel(self):
        """Cancel the career progression event"""
        self.write({'state': 'cancelled'})
        return True

    def action_reset_to_draft(self):
        """Reset to draft state"""
        self.write({'state': 'draft'})
        return True

    @api.model
    def create(self, vals):
        """Override create to set name if not provided"""
        if not vals.get('name') and vals.get('employee_id') and vals.get('event_type'):
            employee = self.env['hr.employee'].browse(vals['employee_id'])
            event_type_label = dict(self._fields['event_type'].selection)[vals['event_type']]
            vals['name'] = f"{employee.name} - {event_type_label}"

        return super().create(vals)

    def write(self, vals):
        """Override write to handle attachment updates"""
        result = super().write(vals)
        return result


class CareerProgressionEventType(models.Model):
    _name = 'career.progression.event.type'
    _description = 'Career Progression Event Type'
    _order = 'sequence, name'

    name = fields.Char(string='Event Type Name', required=True, translate=True)
    code = fields.Char(string='Code', required=True)
    description = fields.Text(string='Description', translate=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)
    color = fields.Integer(string='Color', default=0)

    _sql_constraints = [
        ('code_unique', 'unique(code)', 'Event type code must be unique!')
    ]
