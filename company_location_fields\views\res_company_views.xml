<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit Company Form View -->
        <record id="view_company_form_inherit_location" model="ir.ui.view">
            <field name="name">res.company.form.inherit.location</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="arch" type="xml">
                <!-- Add fields after Currency field -->
                <xpath expr="//field[@name='currency_id']" position="after">
                    <field name="company_country_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select Country..."/>
                    <field name="general_management_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select General Management..."
                           attrs="{'invisible': [('company_country_id', '=', False)]}"/>
                    <field name="company_branch_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select Branch..."/>
                </xpath>
            </field>
        </record>

        <!-- Company Search View -->
        <record id="view_company_search_location" model="ir.ui.view">
            <field name="name">res.company.search.location</field>
            <field name="model">res.company</field>
            <field name="arch" type="xml">
                <search string="Search Companies">
                    <field name="name" string="Company"/>
                    <field name="company_country_id" string="Country"/>
                    <field name="general_management_id" string="General Management"/>
                    <field name="company_branch_id" string="Branch"/>

                    <separator/>
                    <filter string="By Country" name="group_by_country"
                            context="{'group_by': 'company_country_id'}"/>
                    <filter string="By General Management" name="group_by_general_management"
                            context="{'group_by': 'general_management_id'}"/>
                    <filter string="By Branch" name="group_by_branch"
                            context="{'group_by': 'company_branch_id'}"/>
                </search>
            </field>
        </record>

        <!-- Company Action with Search View -->
        <record id="action_company_location_enhanced" model="ir.actions.act_window">
            <field name="name">Companies with Location</field>
            <field name="res_model">res.company</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_company_search_location"/>
            <field name="context">{
                'search_default_group_by_country': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first company!
                </p>
                <p>
                    Companies can be organized by country, general management, and branch.
                </p>
            </field>
        </record>

        <!-- Company Tree View with Location Fields -->
        <record id="view_company_tree_location" model="ir.ui.view">
            <field name="name">res.company.tree.location</field>
            <field name="model">res.company</field>
            <field name="arch" type="xml">
                <tree string="Companies">
                    <field name="name"/>
                    <field name="company_country_id"/>
                    <field name="general_management_id"/>
                    <field name="company_branch_id"/>
                    <field name="phone"/>
                    <field name="email"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>
