# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from datetime import datetime, timedelta
import base64


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    def action_validate(self):
        """
        توسيع دالة الموافقة لإضافة طباعة تقرير الإجازة فوراً
        """
        import logging
        _logger = logging.getLogger(__name__)

        _logger.info("=== بدء عملية الموافقة على الإجازة ===")

        # تنفيذ الموافقة الأصلية أولاً
        result = super(HrLeave, self).action_validate()

        _logger.info("تمت الموافقة الأصلية بنجاح")

        # طباعة تقرير الإجازة فوراً لكل موظف تمت الموافقة على إجازته
        for leave in self:
            _logger.info("فحص الإجازة للموظف: %s", leave.employee_id.name)
            _logger.info("حالة الإجازة: %s", leave.state)
            _logger.info("نوع الإجازة: %s", leave.holiday_status_id.name)

            # التحقق من وجود الحقل أولاً
            if hasattr(leave.holiday_status_id, 'auto_generate_report'):
                _logger.info("إعداد التقرير التلقائي: %s", leave.holiday_status_id.auto_generate_report)

                if leave.state == 'validate' and leave.holiday_status_id.auto_generate_report:
                    _logger.info("بدء تحميل التقرير فوراً")
                    # إرجاع action لتحميل التقرير فوراً
                    return self._download_leave_report_immediately(leave)
                else:
                    _logger.info("شروط إنشاء التقرير غير مستوفاة")
            else:
                _logger.warning("حقل auto_generate_report غير موجود في نوع الإجازة")
                # إنشاء التقرير بشكل افتراضي إذا لم يكن الحقل موجود
                if leave.state == 'validate':
                    _logger.info("تحميل التقرير بشكل افتراضي")
                    return self._download_leave_report_immediately(leave)

        _logger.info("=== انتهاء عملية الموافقة على الإجازة ===")
        return result

    def _download_leave_report_immediately(self, leave):
        """
        تحميل تقرير الإجازة فوراً عند الموافقة
        """
        import logging
        _logger = logging.getLogger(__name__)

        _logger.info("=== بدء تحميل التقرير فوراً ===")

        try:
            # التحقق من وجود الموظف
            if not leave.employee_id:
                _logger.error("لا يوجد موظف مرتبط بهذه الإجازة")
                return {'type': 'ir.actions.act_window_close'}

            _logger.info("الموظف: %s", leave.employee_id.name)

            # التحقق من وجود نموذج الـ wizard
            try:
                wizard_model = self.env['hr.leave.report.wizard']
                _logger.info("نموذج hr.leave.report.wizard موجود")
            except Exception as e:
                _logger.error("نموذج hr.leave.report.wizard غير موجود: %s", str(e))
                return {'type': 'ir.actions.act_window_close'}

            # إنشاء wizard التقرير مع بيانات الإجازة المحددة
            wizard_vals = {
                'date_from': leave.date_from.date() if leave.date_from else fields.Date.today(),
                'date_to': leave.date_to.date() if leave.date_to else fields.Date.today(),
                'employee_id': leave.employee_id.id,
                'all_employees': False,
                'state_filter': 'validate',  # فقط الإجازات الموافق عليها
            }

            _logger.info("قيم الـ wizard: %s", wizard_vals)

            wizard = wizard_model.create(wizard_vals)
            _logger.info("تم إنشاء الـ wizard بنجاح، ID: %s", wizard.id)

            # إنشاء التقرير
            report_data = wizard.get_leave_data()
            _logger.info("بيانات التقرير: %s سجل", len(report_data))

            # التأكد من وجود بيانات للتقرير
            if not report_data:
                _logger.warning("لا توجد بيانات للتقرير")
                # تسجيل رسالة في سجل الإجازة
                leave.message_post(
                    body='تحذير: لا توجد بيانات إجازات لإنشاء التقرير',
                    subtype_xmlid='mail.mt_note'
                )
                return {'type': 'ir.actions.act_window_close'}

            # التحقق من وجود مرجع التقرير
            try:
                report_ref = self.env.ref('hr_leave_reports.hr_leave_report_pdf')
                _logger.info("مرجع التقرير موجود: %s", report_ref.name)
            except Exception as e:
                _logger.error("مرجع التقرير غير موجود: %s", str(e))
                leave.message_post(
                    body='خطأ: مرجع التقرير غير موجود - %s' % str(e),
                    subtype_xmlid='mail.mt_note'
                )
                return {'type': 'ir.actions.act_window_close'}

            # تنسيق تاريخ التقرير
            current_datetime = fields.Datetime.now()
            formatted_date = current_datetime.strftime('%Y-%m-%d %H:%M') if current_datetime else ''

            # إعداد البيانات الأساسية
            data = {
                'date_from': wizard.date_from,
                'date_to': wizard.date_to,
                'employee_name': leave.employee_id.name,
                'all_employees': False,
                'state_filter': 'Approved',
                'leave_data': report_data,
                'total_leaves': len(report_data),
                'total_days': sum(leave_item['number_of_days'] for leave_item in report_data),
                'report_date': formatted_date,
                'show_balance': True,  # عرض الرصيد للموظف المحدد
                'balance_info': {},
                'show_approval': True,  # عرض معلومات الموافقة للموظف المحدد
                'approval_info': {}
            }

            # إضافة معلومات الرصيد للموظف
            if leave.holiday_status_id:
                balance_info = wizard.get_employee_leave_balance(leave.employee_id.id, leave.holiday_status_id.id)
                data['balance_info'] = balance_info
                _logger.info("معلومات الرصيد: %s", balance_info)

            # إضافة معلومات الموافقة للموظف
            approval_info = wizard.get_employee_approval_info(leave.employee_id.id)
            data['approval_info'] = approval_info
            _logger.info("معلومات الموافقة: %s", approval_info)

            _logger.info("بدء تحميل PDF فوراً")

            # تسجيل رسالة في سجل الإجازة
            leave.message_post(
                body='تم إنشاء تقرير الإجازة وتحميله تلقائياً.',
                subtype_xmlid='mail.mt_note'
            )

            _logger.info("تم تحضير التقرير للتحميل الفوري")

            # إرجاع action لتحميل التقرير فوراً
            return report_ref.report_action(wizard, data=data)

        except Exception as e:
            _logger.error("خطأ في تحميل التقرير فوراً: %s", str(e))
            _logger.exception("تفاصيل الخطأ:")

            # في حالة حدوث خطأ، تسجيل الخطأ
            leave.message_post(
                body='خطأ في تحميل تقرير الإجازة فوراً: %s' % str(e),
                subtype_xmlid='mail.mt_note'
            )
            return {'type': 'ir.actions.act_window_close'}

    def _generate_leave_report_for_employee(self, leave):
        """
        إنشاء وطباعة تقرير الإجازة للموظف المحدد
        """
        import logging
        _logger = logging.getLogger(__name__)

        _logger.info("=== بدء إنشاء تقرير الإجازة ===")

        try:
            # التحقق من وجود الموظف
            if not leave.employee_id:
                _logger.error("لا يوجد موظف مرتبط بهذه الإجازة")
                leave.message_post(
                    body='خطأ: لا يوجد موظف مرتبط بهذه الإجازة',
                    subtype_xmlid='mail.mt_note'
                )
                return

            _logger.info("الموظف: %s", leave.employee_id.name)

            # التحقق من وجود نموذج الـ wizard
            try:
                wizard_model = self.env['hr.leave.report.wizard']
                _logger.info("نموذج hr.leave.report.wizard موجود")
            except Exception as e:
                _logger.error("نموذج hr.leave.report.wizard غير موجود: %s", str(e))
                leave.message_post(
                    body='خطأ: نموذج التقرير غير موجود - %s' % str(e),
                    subtype_xmlid='mail.mt_note'
                )
                return

            # إنشاء wizard التقرير مع بيانات الإجازة المحددة
            wizard_vals = {
                'date_from': leave.date_from.date() if leave.date_from else fields.Date.today(),
                'date_to': leave.date_to.date() if leave.date_to else fields.Date.today(),
                'employee_id': leave.employee_id.id,
                'all_employees': False,
                'state_filter': 'validate',  # فقط الإجازات الموافق عليها
            }

            _logger.info("قيم الـ wizard: %s", wizard_vals)

            wizard = wizard_model.create(wizard_vals)
            _logger.info("تم إنشاء الـ wizard بنجاح، ID: %s", wizard.id)

            # إنشاء التقرير
            report_data = wizard.get_leave_data()
            _logger.info("بيانات التقرير: %s سجل", len(report_data))

            # التأكد من وجود بيانات للتقرير
            if not report_data:
                _logger.warning("لا توجد بيانات للتقرير")
                leave.message_post(
                    body='تحذير: لا توجد بيانات إجازات لإنشاء التقرير',
                    subtype_xmlid='mail.mt_note'
                )
                return

            # التحقق من وجود مرجع التقرير
            try:
                report_ref = self.env.ref('hr_leave_reports.hr_leave_report_pdf')
                _logger.info("مرجع التقرير موجود: %s", report_ref.name)
            except Exception as e:
                _logger.error("مرجع التقرير غير موجود: %s", str(e))
                leave.message_post(
                    body='خطأ: مرجع التقرير غير موجود - %s' % str(e),
                    subtype_xmlid='mail.mt_note'
                )
                return

            # تنسيق تاريخ التقرير
            current_datetime = fields.Datetime.now()
            formatted_date = current_datetime.strftime('%Y-%m-%d %H:%M') if current_datetime else ''

            data = {
                'date_from': wizard.date_from,
                'date_to': wizard.date_to,
                'employee_name': leave.employee_id.name,
                'all_employees': False,
                'state_filter': 'Approved',
                'leave_data': report_data,
                'total_leaves': len(report_data),
                'total_days': sum(leave_item['number_of_days'] for leave_item in report_data),
                'report_date': formatted_date,
            }

            _logger.info("بدء إنشاء PDF")

            # إنشاء التقرير كـ PDF
            pdf_content, _ = report_ref._render_qweb_pdf(wizard.ids, data=data)
            _logger.info("تم إنشاء PDF بحجم: %s بايت", len(pdf_content))

            # تسجيل رسالة في سجل الإجازة فقط (بدون إرسال للموظف)
            leave.message_post(
                body='تم إنشاء تقرير الإجازة (للاستخدام الداخلي فقط).',
                subtype_xmlid='mail.mt_note'
            )

            _logger.info("تم إنشاء التقرير بنجاح")

        except Exception as e:
            _logger.error("خطأ في إنشاء التقرير: %s", str(e))
            _logger.exception("تفاصيل الخطأ:")

            # في حالة حدوث خطأ، تسجيل الخطأ دون إيقاف عملية الموافقة
            leave.message_post(
                body='خطأ في إنشاء تقرير الإجازة: %s' % str(e),
                subtype_xmlid='mail.mt_note'
            )

    def action_approve(self):
        """
        توسيع دالة الموافقة الأولى (في حالة الموافقة المزدوجة)
        """
        result = super(HrLeave, self).action_approve()

        # في حالة الموافقة المزدوجة، لا نطبع التقرير هنا
        # سيتم طباعته فقط عند الموافقة النهائية في action_validate

        return result
