# تقارير الإجازات المتقدمة - HR Leave Reports

## الوصف

مديول تقارير الإجازات المتقدمة يوفر تقارير شاملة ومتطورة لإدارة الإجازات في نظام Odoo. هذا المديول مصمم خصيصاً لتلبية احتياجات الشركات العربية مع واجهة مستخدم باللغة العربية وتصميم احترافي.

## المميزات الرئيسية

### 🎯 تقرير شامل للإجازات
- عرض جميع بيانات الإجازات في تقرير واحد
- تصميم احترافي وجذاب بصيغة PDF
- دعم كامل للغة العربية

### 🔍 تصفية متقدمة
- تحديد فترة زمنية مخصصة (من تاريخ - إلى تاريخ)
- اختيار موظف معين أو كل الموظفين
- تصفية حسب حالة الإجازة (مسودة، في انتظار الموافقة، موافق، مرفوض، إلخ)

### 📊 البيانات المعروضة
- **رقم الموظف الوظيفي**: رقم التعريف الخاص بالموظف
- **اسم الموظف**: الاسم الكامل للموظف
- **القسم**: القسم التابع له الموظف
- **نوع الإجازة**: نوع الإجازة (سنوية، مرضية، إلخ)
- **تاريخ البداية**: تاريخ بداية الإجازة
- **تاريخ النهاية**: تاريخ نهاية الإجازة
- **عدد الأيام**: إجمالي أيام الإجازة
- **الحالة**: حالة طلب الإجازة الحالية
- **تاريخ الطلب**: تاريخ تقديم طلب الإجازة

### 📈 إحصائيات مفصلة
- إجمالي عدد الإجازات في الفترة المحددة
- إجمالي عدد الأيام المستخدمة
- متوسط أيام الإجازة لكل طلب

## التثبيت

1. انسخ مجلد `hr_leave_reports` إلى مجلد addons في Odoo
2. قم بتحديث قائمة التطبيقات
3. ابحث عن "تقارير الإجازات المتقدمة"
4. اضغط على "تثبيت"

## الاستخدام

### الوصول للتقرير
1. اذهب إلى **الإجازات** → **التقارير** → **تقرير الإجازات المتقدم**
2. أو من خلال القائمة الرئيسية: **الموارد البشرية** → **الإجازات** → **التقارير**

### إعداد التقرير
1. **حدد الفترة الزمنية**:
   - من تاريخ: تاريخ بداية الفترة المطلوبة
   - إلى تاريخ: تاريخ نهاية الفترة المطلوبة

2. **اختر نطاق الموظفين**:
   - كل الموظفين: لعرض إجازات جميع الموظفين
   - موظف محدد: لعرض إجازات موظف واحد فقط

3. **حدد حالة الإجازة** (اختياري):
   - كل الحالات: لعرض جميع الإجازات بغض النظر عن حالتها
   - حالة محددة: لعرض الإجازات ذات الحالة المحددة فقط

4. **اضغط "طباعة التقرير"** لإنشاء التقرير بصيغة PDF

### الطباعة التلقائية عند الموافقة
1. **تفعيل الميزة**: اذهب إلى **الإجازات** → **الإعدادات** → **أنواع الإجازات**
2. **اختر نوع الإجازة** المطلوب
3. **فعل خيار "إنشاء التقرير تلقائياً عند الموافقة"**
4. **احفظ الإعدادات**

الآن عند الموافقة على أي إجازة من هذا النوع:
- سيتم إنشاء تقرير PDF تلقائياً
- سيتم حفظ التقرير كمرفق في سجل الإجازة
- سيتم إرسال التقرير للموظف عبر البريد الإلكتروني

## المتطلبات

- Odoo 15.0 أو أحدث
- مديول `hr_holidays` (مثبت افتراضياً)

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المديول مرخص تحت رخصة LGPL-3.

---

**ملاحظة**: هذا المديول مصمم خصيصاً للشركات العربية ويدعم اتجاه النص من اليمين إلى اليسار (RTL) بشكل كامل.
