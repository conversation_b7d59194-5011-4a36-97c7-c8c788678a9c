<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.attendance</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="80"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Attendances" string="Attendances" data-key="hr_attendance" groups="hr_attendance.group_hr_attendance_manager">
                    <h2>Check-In/Out</h2>
                    <div class="row mt16 o_settings_container" name="pincode_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box" title="Set PIN codes in the employee detail form (in HR Settings tab).">
                            <div class="o_setting_left_pane">
                                <field name="group_attendance_use_pin"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <span class="o_form_label">Employee PIN</span>
                                <div class="text-muted">
                                    Use PIN codes to check in in Kiosk Mode
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Extra Hours</h2>
                    <div class="row mt16 o_settings_container" name="overtime_settings">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane" title="Activate the count of employees' extra hours.">
                                <field name="hr_attendance_overtime"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." role="img" aria-label="Values set here are company-specific." groups="base.group_multi_company"/>
                                <label for="hr_attendance_overtime" class="o_form_label">Count of Extra Hours</label>
                                <div class="text-muted">
                                    Compare attendance with working hours set on employee.
                                </div>
                                <div class="mt16" attrs="{'invisible': [('hr_attendance_overtime', '=', False)],
                                                          'required': [('hr_attendance_overtime', '=', True)]}">
                                    <div class="mt16 row" title="Count of extra hours is considered from this date. Potential extra hours prior to this date are not considered.">
                                        <label for="overtime_start_date" string="Start from" class="col-3 col-lg-3 o_light_label"/>
                                        <field name="overtime_start_date" class="col-lg-3 p-0" attrs="{'required': [('hr_attendance_overtime', '=', True)]}" />
                                    </div>
                                    <br/>
                                    <label for="overtime_company_threshold" class="o_form_label">
                                        Tolerance Time In Favor Of Company
                                    </label>
                                    <div class="text-muted">
                                        Allow a period of time (around working hours) where extra time will not be counted, in benefit of the company
                                    </div>
                                    <span>Time Period </span><field name="overtime_company_threshold" class="text-center oe_inline"
                                        attrs="{'required': [('hr_attendance_overtime', '=', True)]}"/><span> Minutes</span>
                                    <br/>
                                    <br/>
                                    <label for="overtime_employee_threshold" class="o_form_label">
                                        Tolerance Time In Favor Of Employee
                                    </label>
                                    <div class="text-muted">
                                        Allow a period of time (around working hours) where extra time will not be deducted, in benefit of the employee
                                    </div>
                                    <span>Time Period </span><field name="overtime_employee_threshold" class="text-center oe_inline"
                                        attrs="{'required': [('hr_attendance_overtime', '=', True)]}"/><span> Minutes</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="action_hr_attendance_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'hr_attendance', 'bin_size': False}</field>
    </record>

    <menuitem id="hr_attendance.menu_hr_attendance_settings" name="Configuration" parent="menu_hr_attendance_root"
        sequence="99" action="action_hr_attendance_settings" groups="hr_attendance.group_hr_attendance_manager"/>
</odoo>
