# -*- coding: utf-8 -*-
{
    'name': 'HR Job Title Info',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'Add Job Title Information Tab to Employee Form',
    'description': """
HR Job Title Info
=================

This module adds a new tab "Job Title Info" to the employee form in HR module.
The tab contains detailed information about the employee's current job title including:

- Job Title
- Job Start Date
- Related Department
- Responsibilities
- Job Level
- Additional Notes

Features:
---------
* Adds new tab next to HR Settings tab
* All fields are editable through the form
* Proper access rights and security
* Arabic translation support
* Compatible with Odoo 15 official UI/UX patterns

Author: Custom Development
    """,
    'author': 'Custom Development',
    'website': '',
    'depends': ['hr'],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_employee_views.xml',
    ],
    'demo': [
        'demo/hr_employee_demo.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
