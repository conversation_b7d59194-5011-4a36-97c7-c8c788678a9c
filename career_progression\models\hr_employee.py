# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    career_progression_ids = fields.One2many(
        'career.progression',
        'employee_id',
        string='Career Progression',
        help="Career progression events for this employee"
    )

    assignment_ids = fields.One2many(
        'employee.assignment',
        'employee_id',
        string='Assignments & Tasks',
        help="Assignments, tasks, and training for this employee"
    )

    career_progression_count = fields.Integer(
        string='Career Events Count',
        compute='_compute_career_progression_count',
        help="Number of career progression events"
    )

    assignment_count = fields.Integer(
        string='Assignments Count',
        compute='_compute_assignment_count',
        help="Number of assignments and tasks"
    )

    last_career_event_date = fields.Date(
        string='Last Career Event',
        compute='_compute_last_career_event',
        store=True,
        help="Date of the most recent career progression event"
    )

    last_career_event_type = fields.Char(
        string='Last Event Type',
        compute='_compute_last_career_event',
        store=True,
        help="Type of the most recent career progression event"
    )

    years_in_current_position = fields.Float(
        string='Years in Current Position',
        compute='_compute_years_in_position',
        store=True,
        help="Number of years in current position"
    )

    @api.depends('career_progression_ids')
    def _compute_career_progression_count(self):
        for employee in self:
            employee.career_progression_count = len(employee.career_progression_ids)

    @api.depends('assignment_ids')
    def _compute_assignment_count(self):
        for employee in self:
            employee.assignment_count = len(employee.assignment_ids)

    @api.depends('career_progression_ids.event_date', 'career_progression_ids.event_type', 'career_progression_ids.state')
    def _compute_last_career_event(self):
        for employee in self:
            last_event = employee.career_progression_ids.filtered(
                lambda x: x.state == 'confirmed'
            ).sorted('event_date', reverse=True)[:1]

            if last_event:
                employee.last_career_event_date = last_event.event_date
                employee.last_career_event_type = dict(
                    last_event._fields['event_type'].selection
                )[last_event.event_type]
            else:
                employee.last_career_event_date = False
                employee.last_career_event_type = False

    @api.depends('career_progression_ids.event_date', 'career_progression_ids.state', 'create_date')
    def _compute_years_in_position(self):
        for employee in self:
            # Get the most recent confirmed career event
            last_confirmed_event = employee.career_progression_ids.filtered(
                lambda x: x.state == 'confirmed'
            ).sorted('event_date', reverse=True)[:1]

            if last_confirmed_event:
                # Start counting from the date of the last confirmed career event
                start_date = last_confirmed_event.event_date
            else:
                # If no career events, start from employee creation date
                start_date = employee.create_date.date() if employee.create_date else fields.Date.today()

            today = fields.Date.today()
            years = (today - start_date).days / 365.25
            employee.years_in_current_position = round(years, 1)

    def action_view_career_progression(self):
        """Open career progression view for this employee"""
        self.ensure_one()
        return {
            'name': _('Career Progression - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'career.progression',
            'view_mode': 'tree,form,calendar,graph',
            'domain': [('employee_id', '=', self.id)],
            'context': {
                'default_employee_id': self.id,
                'search_default_employee_id': self.id,
            },
            'target': 'current',
        }

    def action_create_career_event(self):
        """Create a new career progression event for this employee"""
        self.ensure_one()
        return {
            'name': _('New Career Event - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'career.progression',
            'view_mode': 'form',
            'context': {
                'default_employee_id': self.id,
                'default_previous_job_title': self.job_title,
                'default_previous_department_id': self.department_id.id if self.department_id else False,
            },
            'target': 'new',
        }

    def action_create_assignment(self):
        """Create a new assignment for this employee"""
        self.ensure_one()
        return {
            'name': _('New Assignment - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'employee.assignment',
            'view_mode': 'form',
            'context': {
                'default_employee_id': self.id,
                'default_department_id': self.department_id.id if self.department_id else False,
                'default_assigned_by': self.env.user.employee_id.id if self.env.user.employee_id else False,
            },
            'target': 'new',
        }

    def get_career_timeline_data(self):
        """Get career timeline data for visualization"""
        self.ensure_one()
        events = []

        for progression in self.career_progression_ids.filtered(lambda x: x.state == 'confirmed').sorted('event_date'):
            event_data = {
                'id': progression.id,
                'date': progression.event_date,
                'title': progression.name,
                'type': progression.event_type,
                'type_label': dict(progression._fields['event_type'].selection)[progression.event_type],
                'previous_title': progression.previous_job_title,
                'new_title': progression.new_job_id.name if progression.new_job_id else '',
                'previous_department': progression.previous_department_id.name if progression.previous_department_id else '',
                'new_department': progression.new_department_id.name if progression.new_department_id else '',
                'reason': progression.reason,
                'is_temporary': progression.is_temporary,
                'temporary_end_date': progression.temporary_end_date if progression.is_temporary else None,
            }
            events.append(event_data)

        return events
