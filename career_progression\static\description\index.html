<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Career Progression - Employee Career Path Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .feature h3 {
            color: #2c3e50;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            font-size: 1.5em;
            margin-right: 10px;
            color: #3498db;
        }
        .highlight {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #27ae60;
            margin: 20px 0;
        }
        .tech-specs {
            background: #f1f2f6;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .tech-specs h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .tech-specs ul {
            list-style-type: none;
            padding: 0;
        }
        .tech-specs li {
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        .tech-specs li:last-child {
            border-bottom: none;
        }
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Career Progression</h1>
            <p>Employee Career Path Management for Odoo 15</p>
        </div>
        
        <div class="content">
            <div class="highlight">
                <h2>📈 Professional Career Tracking Solution</h2>
                <p>A comprehensive module for tracking and managing employee career progression within your organization. Record promotions, transfers, title changes, and temporary assignments with a professional timeline interface.</p>
            </div>
            
            <div class="features">
                <div class="feature">
                    <h3><span class="feature-icon">📋</span>Career Path Tab</h3>
                    <p>New "Career Path" tab in employee form with professional timeline view and career summary cards.</p>
                </div>
                
                <div class="feature">
                    <h3><span class="feature-icon">🔄</span>Event Types</h3>
                    <p>Track transfers, promotions, title changes, temporary assignments, department changes, and location changes.</p>
                </div>
                
                <div class="feature">
                    <h3><span class="feature-icon">📊</span>Multiple Views</h3>
                    <p>Tree, Form, Timeline, and Kanban views for comprehensive career progression management.</p>
                </div>
                
                <div class="feature">
                    <h3><span class="feature-icon">📎</span>Document Support</h3>
                    <p>Attach supporting documents like promotion letters, transfer orders, and other career-related files.</p>
                </div>
                
                <div class="feature">
                    <h3><span class="feature-icon">⏰</span>Temporary Assignments</h3>
                    <p>Special handling for temporary assignments with start and end dates tracking.</p>
                </div>
                
                <div class="feature">
                    <h3><span class="feature-icon">🔍</span>Advanced Search</h3>
                    <p>Powerful search and filtering capabilities with grouping by employee, event type, department, and date.</p>
                </div>
            </div>
            
            <div class="tech-specs">
                <h3>🛠️ Technical Specifications</h3>
                <ul>
                    <li><strong>Odoo Version:</strong> 15.0+</li>
                    <li><strong>Dependencies:</strong> hr, mail</li>
                    <li><strong>License:</strong> LGPL-3</li>
                    <li><strong>Models:</strong> career.progression, career.progression.event.type</li>
                    <li><strong>Views:</strong> Tree, Form, Timeline, Kanban, Search</li>
                    <li><strong>Security:</strong> Role-based access control</li>
                    <li><strong>Features:</strong> Mail integration, Activity tracking, Document attachments</li>
                </ul>
            </div>
            
            <div class="highlight">
                <h3>✨ Key Benefits</h3>
                <ul>
                    <li>📈 Complete career progression tracking</li>
                    <li>🎯 Professional timeline visualization</li>
                    <li>📋 Comprehensive event documentation</li>
                    <li>🔐 Role-based security and permissions</li>
                    <li>📊 Statistical insights and reporting</li>
                    <li>🔄 Seamless integration with HR module</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 Career Progression Module | Built for Odoo 15 | Following Official Odoo Standards</p>
        </div>
    </div>
</body>
</html>
